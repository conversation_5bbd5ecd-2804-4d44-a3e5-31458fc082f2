{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuthStore } from '@/store/useAuthStore';\n\nexport default function SignIn() {\n  const router = useRouter();\n  const { signIn, loading, error, clearError } = useAuthStore();\n  \n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    clearError();\n    \n    try {\n      await signIn(formData.email, formData.password);\n      router.push('/dashboard');\n    } catch (error) {\n      // Error is handled by the store\n      console.error('Sign in failed:', error);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }));\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n            <p className=\"text-gray-600\">智能时间规划助手</p>\n          </div>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                邮箱地址\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                placeholder=\"请输入邮箱地址\"\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                密码\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                value={formData.password}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                placeholder=\"请输入密码\"\n              />\n            </div>\n            \n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-600\">{error}</p>\n              </div>\n            )}\n            \n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  登录中...\n                </div>\n              ) : (\n                '登录'\n              )}\n            </button>\n          </form>\n          \n          <div className=\"mt-6 text-center\">\n            <p className=\"text-sm text-gray-600\">\n              还没有账户？{' '}\n              <Link href=\"/auth/signup\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n                立即注册\n              </Link>\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500\">\n            使用 TimeManager 即表示您同意我们的服务条款和隐私政策\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB;QAEA,IAAI;YACF,MAAM,OAAO,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC9C,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,gCAAgC;YAChC,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAIf,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAIzC,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;sCAKN,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCAC5B;kDACP,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;;;;;;8BAO9F,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}