-- 修复 user_profiles 表结构
-- 添加缺失的字段

-- 1. 添加 time_config 字段（如果不存在）
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS time_config JSONB DEFAULT '{}';

-- 2. 添加 onboarding_completed 字段（如果不存在）
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;

-- 3. 确保 work_hours 字段支持 days 数组
-- 这个字段应该已经是 JSONB 类型，可以存储复杂对象

-- 4. 检查并创建必要的索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(onboarding_completed);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- 5. 更新现有记录的默认值（如果需要）
UPDATE user_profiles 
SET time_config = '{}' 
WHERE time_config IS NULL;

UPDATE user_profiles 
SET onboarding_completed = FALSE 
WHERE onboarding_completed IS NULL;

-- 6. 验证表结构
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
ORDER BY ordinal_position;
