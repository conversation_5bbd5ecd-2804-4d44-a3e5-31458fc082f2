{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/hooks/useAuthInit.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { supabase } from '@/lib/supabase';\n\nexport function useAuthInit() {\n  const { user, setUser, setLoading } = useAuthStore();\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  useEffect(() => {\n    let mounted = true;\n\n    const initializeAuth = async () => {\n      console.log('🔧 useAuthInit: Initializing auth state...');\n      \n      try {\n        setLoading(true);\n        \n        // 首先检查 localStorage 中是否有持久化的用户数据\n        const storedAuth = localStorage.getItem('timemanager-auth');\n        if (storedAuth) {\n          try {\n            const parsed = JSON.parse(storedAuth);\n            console.log('💾 Found stored auth data:', parsed.state?.user?.id || 'none');\n          } catch (e) {\n            console.log('❌ Error parsing stored auth data');\n          }\n        }\n\n        // 从 Supabase 获取当前认证状态\n        const { data: { user: currentUser }, error } = await supabase.auth.getUser();\n        \n        console.log('👤 Supabase current user:', currentUser?.id || 'none');\n        console.log('❌ Supabase error:', error?.message || 'none');\n        \n        if (mounted) {\n          if (currentUser) {\n            setUser(currentUser);\n            console.log('✅ Auth initialized with user:', currentUser.email);\n          } else {\n            setUser(null);\n            console.log('🔐 Auth initialized without user');\n          }\n        }\n      } catch (error) {\n        console.error('❌ Auth initialization failed:', error);\n        if (mounted) {\n          setUser(null);\n        }\n      } finally {\n        if (mounted) {\n          setLoading(false);\n          setIsInitialized(true);\n          console.log('🎯 Auth initialization complete');\n        }\n      }\n    };\n\n    initializeAuth();\n\n    return () => {\n      mounted = false;\n    };\n  }, [setUser, setLoading]);\n\n  return { isInitialized, user };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,UAAU;YAEd,MAAM;wDAAiB;oBACrB,QAAQ,GAAG,CAAC;oBAEZ,IAAI;wBACF,WAAW;wBAEX,iCAAiC;wBACjC,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,YAAY;4BACd,IAAI;gCACF,MAAM,SAAS,KAAK,KAAK,CAAC;gCAC1B,QAAQ,GAAG,CAAC,8BAA8B,OAAO,KAAK,EAAE,MAAM,MAAM;4BACtE,EAAE,OAAO,GAAG;gCACV,QAAQ,GAAG,CAAC;4BACd;wBACF;wBAEA,sBAAsB;wBACtB,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;wBAE1E,QAAQ,GAAG,CAAC,6BAA6B,aAAa,MAAM;wBAC5D,QAAQ,GAAG,CAAC,qBAAqB,OAAO,WAAW;wBAEnD,IAAI,SAAS;4BACX,IAAI,aAAa;gCACf,QAAQ;gCACR,QAAQ,GAAG,CAAC,iCAAiC,YAAY,KAAK;4BAChE,OAAO;gCACL,QAAQ;gCACR,QAAQ,GAAG,CAAC;4BACd;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,IAAI,SAAS;4BACX,QAAQ;wBACV;oBACF,SAAU;wBACR,IAAI,SAAS;4BACX,WAAW;4BACX,iBAAiB;4BACjB,QAAQ,GAAG,CAAC;wBACd;oBACF;gBACF;;YAEA;YAEA;yCAAO;oBACL,UAAU;gBACZ;;QACF;gCAAG;QAAC;QAAS;KAAW;IAExB,OAAO;QAAE;QAAe;IAAK;AAC/B;GA7DgB;;QACwB,+HAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useAuthInit } from '@/hooks/useAuthInit';\nimport { supabase } from '@/lib/supabase';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, loading } = useAuthStore();\n  const { isInitialized } = useAuthInit();\n  const [isMounted, setIsMounted] = useState(false);\n\n  // 防止水合错误\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!isMounted || !isInitialized) return;\n\n    const handleRedirect = async () => {\n      console.log('🏠 Home page - handling redirect...');\n      console.log('👤 Current user:', user?.id || 'none');\n      console.log('⏳ Loading state:', loading);\n\n      // 如果正在加载，等待\n      if (loading) {\n        console.log('⏳ Still loading, waiting...');\n        return;\n      }\n\n      if (user) {\n        console.log('✅ User found, checking onboarding...');\n        try {\n          const { data: profile, error: profileError } = await supabase\n            .from('user_profiles')\n            .select('onboarding_completed')\n            .eq('id', user.id)\n            .single();\n\n          console.log('📋 Profile data:', profile);\n\n          if (profileError) {\n            console.log('🚀 Profile error, redirecting to onboarding...');\n            router.push('/onboarding');\n          } else if (profile?.onboarding_completed) {\n            console.log('✅ Onboarding completed, redirecting to dashboard...');\n            router.push('/dashboard');\n          } else {\n            console.log('📝 Onboarding not completed, redirecting to onboarding...');\n            router.push('/onboarding');\n          }\n        } catch (error) {\n          console.error('❌ Error checking profile:', error);\n          router.push('/onboarding');\n        }\n      } else {\n        console.log('🔐 No user found, redirecting to signin...');\n        router.push('/auth/signin');\n      }\n    };\n\n    handleRedirect();\n  }, [isMounted, isInitialized, user, loading, router]);\n\n  // 防止水合错误：在服务端和客户端挂载前显示相同内容\n  if (!isMounted || !isInitialized || loading) {\n\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n          <p className=\"text-gray-600\">智能时间规划助手</p>\n          <p className=\"text-sm text-gray-500 mt-2\">正在检查登录状态...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // 这里不应该到达，因为上面的 useEffect 会处理重定向\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;QACf;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,aAAa,CAAC,eAAe;YAElC,MAAM;iDAAiB;oBACrB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,oBAAoB,MAAM,MAAM;oBAC5C,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,YAAY;oBACZ,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC;wBACZ;oBACF;oBAEA,IAAI,MAAM;wBACR,QAAQ,GAAG,CAAC;wBACZ,IAAI;4BACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,iBACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;4BAET,QAAQ,GAAG,CAAC,oBAAoB;4BAEhC,IAAI,cAAc;gCAChB,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO,IAAI,SAAS,sBAAsB;gCACxC,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO;gCACL,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,OAAO,IAAI,CAAC;wBACd;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAW;QAAe;QAAM;QAAS;KAAO;IAEpD,2BAA2B;IAC3B,IAAI,CAAC,aAAa,CAAC,iBAAiB,SAAS;QAE3C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,iCAAiC;IACjC,OAAO;AACT;GA5EwB;;QACP,qIAAA,CAAA,YAAS;QACE,+HAAA,CAAA,eAAY;QACZ,8HAAA,CAAA,cAAW;;;KAHf", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}