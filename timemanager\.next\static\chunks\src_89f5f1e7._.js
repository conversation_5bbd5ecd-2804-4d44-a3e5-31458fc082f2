(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "auth": (()=>auth),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://vnkdibuupyelcopkqsev.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZua2RpYnV1cHllbGNvcGtxc2V2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0NjY0NDAsImV4cCI6MjA2ODA0MjQ0MH0.RIIGHnOKWz0dHOLLnQMagF182tZe3viYIanPegeHb2A");
// 检查是否配置了有效的Supabase环境变量
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_url_here' && supabaseAnonKey !== 'your_supabase_anon_key_here' && supabaseUrl.startsWith('https://');
let supabaseClient = null;
if (isSupabaseConfigured) {
    supabaseClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey, {
        auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true
        },
        realtime: {
            params: {
                eventsPerSecond: 10
            }
        }
    });
} else {
    console.warn('⚠️ Supabase not configured. Running in development mode without database.');
    // 创建一个模拟的Supabase客户端用于开发
    supabaseClient = {
        auth: {
            signUp: ()=>Promise.resolve({
                    data: null,
                    error: new Error('Supabase not configured')
                }),
            signInWithPassword: ()=>Promise.resolve({
                    data: null,
                    error: new Error('Supabase not configured')
                }),
            signOut: ()=>Promise.resolve({
                    error: null
                }),
            getUser: ()=>Promise.resolve({
                    data: {
                        user: null
                    },
                    error: null
                }),
            onAuthStateChange: ()=>({
                    data: {
                        subscription: {
                            unsubscribe: ()=>{}
                        }
                    }
                })
        },
        from: ()=>({
                select: ()=>({
                        eq: ()=>({
                                order: ()=>Promise.resolve({
                                        data: [],
                                        error: null
                                    })
                            })
                    }),
                insert: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: ()=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    }),
                delete: ()=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            })
    };
}
const supabase = supabaseClient;
const auth = {
    signUp: async (email, password)=>{
        return await supabase.auth.signUp({
            email,
            password
        });
    },
    signIn: async (email, password)=>{
        return await supabase.auth.signInWithPassword({
            email,
            password
        });
    },
    signOut: async ()=>{
        return await supabase.auth.signOut();
    },
    getCurrentUser: async ()=>{
        const { data: { user } } = await supabase.auth.getUser();
        return user;
    },
    onAuthStateChange: (callback)=>{
        return supabase.auth.onAuthStateChange(callback);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/useAuthStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        loading: false,
        error: null,
        signIn: async (email, password)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"].signIn(email, password);
                if (error) {
                    throw new Error(error.message);
                }
                if (data.user) {
                    set({
                        user: data.user,
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        signUp: async (email, password)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"].signUp(email, password);
                if (error) {
                    throw new Error(error.message);
                }
                if (data.user) {
                    set({
                        user: data.user,
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        signOut: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"].signOut();
                if (error) {
                    throw new Error(error.message);
                }
                set({
                    user: null,
                    loading: false
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        setUser: (user)=>{
            set({
                user
            });
        },
        setLoading: (loading)=>{
            set({
                loading
            });
        },
        setError: (error)=>{
            set({
                error
            });
        },
        clearError: ()=>{
            set({
                error: null
            });
        }
    }), {
    name: 'timemanager-auth',
    partialize: (state)=>({
            user: state.user
        }),
    // 确保在服务端渲染时不会出现水合错误
    skipHydration: false
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AuthProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AuthProvider({ children }) {
    _s();
    const { setUser, setLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            let mounted = true;
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    console.log('🔄 Initializing auth...');
                    try {
                        setLoading(true);
                        // 从 Supabase 获取当前用户状态
                        const { data: { user: currentUser }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getUser();
                        console.log('👤 Current user:', currentUser?.id || 'none');
                        console.log('❌ Auth error:', error?.message || 'none');
                        if (mounted) {
                            setUser(currentUser);
                            console.log('✅ Auth initialized successfully');
                        }
                    } catch (error) {
                        console.error('❌ Auth initialization failed:', error);
                        if (mounted) {
                            setUser(null);
                        }
                    } finally{
                        if (mounted) {
                            setLoading(false);
                            setIsInitialized(true);
                            console.log('🎯 Auth initialization complete');
                        }
                    }
                }
            }["AuthProvider.useEffect.initializeAuth"];
            // 监听认证状态变化
            const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange({
                "AuthProvider.useEffect": (event, session)=>{
                    console.log('🔄 Auth state changed:', event, session?.user?.id || 'none');
                    if (mounted) {
                        setUser(session?.user || null);
                    }
                }
            }["AuthProvider.useEffect"]);
            // 初始化认证状态
            initializeAuth();
            return ({
                "AuthProvider.useEffect": ()=>{
                    mounted = false;
                    subscription.unsubscribe();
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []); // 空依赖数组，只在组件挂载时运行一次
    // 在初始化完成前显示加载状态
    if (!isInitialized) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthProvider.tsx",
                        lineNumber: 73,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-gray-900 mb-2",
                        children: "TimeManager"
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthProvider.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "智能时间规划助手"
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthProvider.tsx",
                        lineNumber: 75,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-500 mt-2",
                        children: "正在初始化认证状态..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthProvider.tsx",
                        lineNumber: 76,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs text-gray-400 mt-1",
                        children: "请查看控制台日志"
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthProvider.tsx",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/AuthProvider.tsx",
                lineNumber: 72,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/AuthProvider.tsx",
            lineNumber: 71,
            columnNumber: 7
        }, this);
    }
    console.log('🎉 AuthProvider initialized, rendering children');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthProvider, "5zgN9ScFVBE9xdXSE0Pv8iErrK0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"]
    ];
});
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_89f5f1e7._.js.map