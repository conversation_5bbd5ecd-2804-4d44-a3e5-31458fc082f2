'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuthStore();

  useEffect(() => {
    // AuthProvider 已经处理了认证逻辑，这里只需要简单重定向
    if (!loading) {
      if (user) {
        // 用户已登录，重定向到 dashboard（AuthProvider 会处理 onboarding 检查）
        router.push('/dashboard');
      } else {
        // 用户未登录，重定向到登录页
        router.push('/auth/signin');
      }
    }
  }, [user, loading, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">TimeManager</h1>
        <p className="text-gray-600">智能时间规划助手</p>
        <p className="text-sm text-gray-500 mt-2">正在检查登录状态...</p>
      </div>
    </div>
  );
}
