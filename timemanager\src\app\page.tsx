'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';

export default function Home() {
  const router = useRouter();
  const { user, setUser, loading, setLoading } = useAuthStore();
  const [isMounted, setIsMounted] = useState(false);

  // 防止水合错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const initializeAndRedirect = async () => {
      console.log('🏠 Home page - initializing...');

      try {
        setLoading(true);

        // 获取当前用户状态
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();

        console.log('👤 Current user:', currentUser?.id || 'none');

        if (error) {
          console.error('❌ Auth error:', error);
          setUser(null);
          router.push('/auth/signin');
          return;
        }

        setUser(currentUser);

        if (currentUser) {
          console.log('👤 User logged in, checking onboarding status...');
          try {
            const { data: profile, error: profileError } = await supabase
              .from('user_profiles')
              .select('onboarding_completed')
              .eq('id', currentUser.id)
              .single();

            console.log('📋 Profile data:', profile);
            console.log('❌ Profile error:', profileError);

            if (profileError) {
              console.log('🚀 Profile error, redirecting to onboarding...');
              router.push('/onboarding');
            } else if (profile?.onboarding_completed) {
              console.log('✅ Onboarding completed, redirecting to dashboard...');
              router.push('/dashboard');
            } else {
              console.log('📝 Onboarding not completed, redirecting to onboarding...');
              router.push('/onboarding');
            }
          } catch (error) {
            console.error('❌ Error checking profile:', error);
            router.push('/onboarding');
          }
        } else {
          console.log('🔐 No user, redirecting to signin...');
          router.push('/auth/signin');
        }
      } catch (error) {
        console.error('❌ Initialization error:', error);
        router.push('/auth/signin');
      } finally {
        setLoading(false);
      }
    };

    initializeAndRedirect();
  }, [isMounted, router, setUser, setLoading]);

  // 防止水合错误：在服务端和客户端挂载前显示相同内容
  if (!isMounted || loading) {

    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">TimeManager</h1>
          <p className="text-gray-600">智能时间规划助手</p>
          <p className="text-sm text-gray-500 mt-2">正在检查登录状态...</p>
        </div>
      </div>
    );
  }

  // 这里不应该到达，因为上面的 useEffect 会处理重定向
  return null;
}
