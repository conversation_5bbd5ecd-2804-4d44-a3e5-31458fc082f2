'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuthStore();

  useEffect(() => {
    const handleRedirect = async () => {
      console.log('🏠 Home page - checking auth state:', { user: user?.id, loading });

      if (loading) {
        console.log('⏳ Still loading, waiting...');
        return;
      }

      if (user) {
        console.log('👤 User logged in, checking onboarding status...');
        try {
          const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('onboarding_completed')
            .eq('id', user.id)
            .single();

          console.log('📋 Profile data:', profile);
          console.log('❌ Profile error:', error);

          if (error) {
            console.log('🚀 Profile error, redirecting to onboarding...');
            router.push('/onboarding');
          } else if (profile?.onboarding_completed) {
            console.log('✅ Onboarding completed, redirecting to dashboard...');
            router.push('/dashboard');
          } else {
            console.log('📝 Onboarding not completed, redirecting to onboarding...');
            router.push('/onboarding');
          }
        } catch (error) {
          console.error('❌ Error checking profile:', error);
          router.push('/onboarding');
        }
      } else {
        console.log('🔐 No user, redirecting to signin...');
        router.push('/auth/signin');
      }
    };

    handleRedirect();
  }, [user, loading, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">TimeManager</h1>
        <p className="text-gray-600">智能时间规划助手</p>
        <p className="text-sm text-gray-500 mt-2">正在检查登录状态...</p>
      </div>
    </div>
  );
}
