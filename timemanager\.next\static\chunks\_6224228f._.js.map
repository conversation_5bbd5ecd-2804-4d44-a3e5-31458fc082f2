{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/lib/algorithms/planning.ts"], "sourcesContent": ["import { Task, ScoredTask, DailySchedule, TimeSlot } from '@/types';\n\nexport class PlanningAlgorithm {\n  /**\n   * 计算任务的综合分数\n   */\n  calculateTaskScore(task: Task): number {\n    // 基础分数：重要性权重0.6，紧急性权重0.4\n    const baseScore = task.importance * 0.6 + task.urgency * 0.4;\n    \n    // 分类加权\n    const categoryBonus = {\n      work: 0,\n      improvement: 2,\n      entertainment: 1\n    }[task.category];\n    \n    // 推迟惩罚：每推迟一次扣3分\n    const postponePenalty = task.postponeCount * 3;\n    \n    // 截止时间紧迫性加权\n    const deadlineBonus = this.calculateDeadlineUrgency(task.deadline);\n    \n    return baseScore + categoryBonus + postponePenalty + deadlineBonus;\n  }\n  \n  /**\n   * 根据截止时间计算紧迫性加权\n   */\n  private calculateDeadlineUrgency(deadline: Date): number {\n    const now = new Date();\n    const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);\n    \n    if (hoursUntilDeadline < 0) return 10; // 已过期，最高优先级\n    if (hoursUntilDeadline < 2) return 8;  // 2小时内\n    if (hoursUntilDeadline < 6) return 6;  // 6小时内\n    if (hoursUntilDeadline < 24) return 4; // 24小时内\n    if (hoursUntilDeadline < 72) return 2; // 3天内\n    return 0; // 3天以上\n  }\n  \n  /**\n   * 根据重要性和紧急性确定四象限\n   */\n  classifyQuadrant(importance: number, urgency: number): 1 | 2 | 3 | 4 {\n    const isImportant = importance >= 4;\n    const isUrgent = urgency >= 4;\n    \n    if (isImportant && isUrgent) return 1;      // 重要且紧急\n    if (isImportant && !isUrgent) return 2;    // 重要不紧急\n    if (!isImportant && isUrgent) return 3;    // 不重要但紧急\n    return 4;                                   // 不重要不紧急\n  }\n  \n  /**\n   * 生成今日时间安排\n   */\n  generateDailySchedule(tasks: Task[], workHours = { start: '09:00', end: '18:00' }): DailySchedule {\n    // 1. 过滤今日需要处理的任务\n    const todayTasks = this.filterTodayTasks(tasks);\n    \n    // 2. 计算分数并分类\n    const scoredTasks: ScoredTask[] = todayTasks\n      .map(task => ({\n        ...task,\n        score: this.calculateTaskScore(task),\n        quadrant: this.classifyQuadrant(task.importance, task.urgency)\n      }))\n      .sort((a, b) => {\n        // 先按象限排序，再按分数排序\n        if (a.quadrant !== b.quadrant) {\n          return a.quadrant - b.quadrant;\n        }\n        return b.score - a.score;\n      });\n    \n    // 3. 生成时间段\n    const timeSlots = this.generateTimeSlots(scoredTasks, workHours);\n    \n    // 4. 计算总时长\n    const totalDuration = timeSlots.reduce((sum, slot) => \n      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0\n    );\n    \n    return {\n      date: new Date(),\n      timeSlots,\n      totalTasks: todayTasks.length,\n      estimatedDuration: totalDuration\n    };\n  }\n  \n  /**\n   * 过滤今日需要处理的任务\n   */\n  private filterTodayTasks(tasks: Task[]): Task[] {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    \n    return tasks.filter(task => {\n      // 包含今日截止的任务和未完成的高优先级任务\n      const isToday = task.deadline <= tomorrow;\n      const isHighPriority = task.importance >= 4 || task.urgency >= 4;\n      const isPending = task.status === 'pending' || task.status === 'in-progress';\n      \n      return isPending && (isToday || isHighPriority);\n    });\n  }\n  \n  /**\n   * 生成时间段安排\n   */\n  private generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {\n    const timeSlots: TimeSlot[] = [];\n    const today = new Date();\n    \n    // 解析工作时间\n    const [startHour, startMinute] = workHours.start.split(':').map(Number);\n    const [endHour, endMinute] = workHours.end.split(':').map(Number);\n    \n    let currentTime = new Date(today);\n    currentTime.setHours(startHour, startMinute, 0, 0);\n    \n    const workEndTime = new Date(today);\n    workEndTime.setHours(endHour, endMinute, 0, 0);\n    \n    for (const task of tasks) {\n      // 检查是否还有足够的工作时间\n      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\n      \n      if (remainingWorkTime < taskDuration) {\n        // 如果当天时间不够，跳过或安排到明天\n        continue;\n      }\n      \n      const endTime = new Date(currentTime.getTime() + taskDuration);\n      \n      timeSlots.push({\n        task,\n        startTime: new Date(currentTime),\n        endTime,\n        isFixed: false\n      });\n      \n      // 更新当前时间，添加15分钟休息时间\n      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\n      \n      // 如果超过工作时间，停止安排\n      if (currentTime >= workEndTime) {\n        break;\n      }\n    }\n    \n    return timeSlots;\n  }\n  \n  /**\n   * 获取四象限的描述\n   */\n  getQuadrantDescription(quadrant: 1 | 2 | 3 | 4): string {\n    const descriptions = {\n      1: '重要且紧急 - 立即执行',\n      2: '重要不紧急 - 计划执行',\n      3: '不重要但紧急 - 委托处理',\n      4: '不重要不紧急 - 减少或删除'\n    };\n    return descriptions[quadrant];\n  }\n  \n  /**\n   * 获取任务建议\n   */\n  getTaskRecommendation(task: ScoredTask): string {\n    if (task.quadrant === 1) {\n      return '🔥 高优先级任务，建议立即处理';\n    } else if (task.quadrant === 2) {\n      return '📅 重要任务，建议合理安排时间';\n    } else if (task.quadrant === 3) {\n      return '⚡ 紧急但不重要，考虑委托或快速处理';\n    } else {\n      return '🤔 优先级较低，可以延后或删除';\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX;;GAEC,GACD,mBAAmB,IAAU,EAAU;QACrC,yBAAyB;QACzB,MAAM,YAAY,KAAK,UAAU,GAAG,MAAM,KAAK,OAAO,GAAG;QAEzD,OAAO;QACP,MAAM,gBAAgB;YACpB,MAAM;YACN,aAAa;YACb,eAAe;QACjB,CAAC,CAAC,KAAK,QAAQ,CAAC;QAEhB,gBAAgB;QAChB,MAAM,kBAAkB,KAAK,aAAa,GAAG;QAE7C,YAAY;QACZ,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ;QAEjE,OAAO,YAAY,gBAAgB,kBAAkB;IACvD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,QAAc,EAAU;QACvD,MAAM,MAAM,IAAI;QAChB,MAAM,qBAAqB,CAAC,SAAS,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,qBAAqB,GAAG,OAAO,IAAI,YAAY;QACnD,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,IAAI,OAAO,GAAG,QAAQ;QAC/C,IAAI,qBAAqB,IAAI,OAAO,GAAG,MAAM;QAC7C,OAAO,GAAG,OAAO;IACnB;IAEA;;GAEC,GACD,iBAAiB,UAAkB,EAAE,OAAe,EAAiB;QACnE,MAAM,cAAc,cAAc;QAClC,MAAM,WAAW,WAAW;QAE5B,IAAI,eAAe,UAAU,OAAO,GAAQ,QAAQ;QACpD,IAAI,eAAe,CAAC,UAAU,OAAO,GAAM,QAAQ;QACnD,IAAI,CAAC,eAAe,UAAU,OAAO,GAAM,SAAS;QACpD,OAAO,GAAqC,SAAS;IACvD;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAE,YAAY;QAAE,OAAO;QAAS,KAAK;IAAQ,CAAC,EAAiB;QAChG,iBAAiB;QACjB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAEzC,aAAa;QACb,MAAM,cAA4B,WAC/B,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,OAAO,IAAI,CAAC,kBAAkB,CAAC;gBAC/B,UAAU,IAAI,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,CAAC,GACA,IAAI,CAAC,CAAC,GAAG;YACR,gBAAgB;YAChB,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;QAEF,WAAW;QACX,MAAM,YAAY,IAAI,CAAC,iBAAiB,CAAC,aAAa;QAEtD,WAAW;QACX,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC,KAAK,OAC3C,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG;QAG3E,OAAO;YACL,MAAM,IAAI;YACV;YACA,YAAY,WAAW,MAAM;YAC7B,mBAAmB;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,KAAa,EAAU;QAC9C,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,uBAAuB;YACvB,MAAM,UAAU,KAAK,QAAQ,IAAI;YACjC,MAAM,iBAAiB,KAAK,UAAU,IAAI,KAAK,KAAK,OAAO,IAAI;YAC/D,MAAM,YAAY,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;YAE/D,OAAO,aAAa,CAAC,WAAW,cAAc;QAChD;IACF;IAEA;;GAEC,GACD,AAAQ,kBAAkB,KAAmB,EAAE,SAAyC,EAAc;QACpG,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,SAAS;QACT,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAChE,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAE1D,IAAI,cAAc,IAAI,KAAK;QAC3B,YAAY,QAAQ,CAAC,WAAW,aAAa,GAAG;QAEhD,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC,SAAS,WAAW,GAAG;QAE5C,KAAK,MAAM,QAAQ,MAAO;YACxB,gBAAgB;YAChB,MAAM,oBAAoB,YAAY,OAAO,KAAK,YAAY,OAAO;YACrE,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,IAAI,oBAAoB,cAAc;gBAEpC;YACF;YAEA,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;YAEjD,UAAU,IAAI,CAAC;gBACb;gBACA,WAAW,IAAI,KAAK;gBACpB;gBACA,SAAS;YACX;YAEA,oBAAoB;YACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;YAErD,gBAAgB;YAChB,IAAI,eAAe,aAAa;gBAC9B;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,uBAAuB,QAAuB,EAAU;QACtD,MAAM,eAAe;YACnB,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,YAAY,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,sBAAsB,IAAgB,EAAU;QAC9C,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/lib/algorithms/balance.ts"], "sourcesContent": ["import { CategoryRatios, BalanceAnalysis, DailyStats } from '@/types';\nimport { supabase } from '@/lib/supabase';\n\nexport class BalanceAlgorithm {\n  // 理想的时间分配比例\n  private readonly IDEAL_RATIOS: CategoryRatios = {\n    work: 0.6,\n    improvement: 0.25,\n    entertainment: 0.15\n  };\n  \n  /**\n   * 分析用户的生活平衡状况\n   */\n  async analyzeWeeklyBalance(userId: string): Promise<BalanceAnalysis> {\n    try {\n      // 获取最近7天的统计数据\n      const stats = await this.getDailyStats(userId, 7);\n      \n      if (stats.length === 0) {\n        return {\n          workRatio: 0,\n          improvementRatio: 0,\n          entertainmentRatio: 0,\n          balanceScore: 0,\n          recommendation: \"开始记录你的任务来获得生活平衡分析 📊\"\n        };\n      }\n      \n      // 计算总时间和各分类时间\n      const totalTime = stats.reduce((sum, day) => \n        sum + day.workTime + day.improvementTime + day.entertainmentTime, 0\n      );\n      \n      if (totalTime === 0) {\n        return {\n          workRatio: 0,\n          improvementRatio: 0,\n          entertainmentRatio: 0,\n          balanceScore: 0,\n          recommendation: \"还没有完成任务记录，开始你的第一个任务吧！ 🚀\"\n        };\n      }\n      \n      // 计算各分类的时间比例\n      const ratios: CategoryRatios = {\n        work: stats.reduce((sum, day) => sum + day.workTime, 0) / totalTime,\n        improvement: stats.reduce((sum, day) => sum + day.improvementTime, 0) / totalTime,\n        entertainment: stats.reduce((sum, day) => sum + day.entertainmentTime, 0) / totalTime\n      };\n      \n      // 计算平衡分数\n      const balanceScore = this.calculateBalanceScore(ratios);\n      \n      // 生成建议\n      const recommendation = this.generateRecommendation(ratios, stats);\n      \n      return {\n        workRatio: ratios.work,\n        improvementRatio: ratios.improvement,\n        entertainmentRatio: ratios.entertainment,\n        balanceScore,\n        recommendation\n      };\n    } catch (error) {\n      console.error('Error analyzing balance:', error);\n      throw new Error('Failed to analyze balance');\n    }\n  }\n  \n  /**\n   * 计算生活平衡分数 (0-100)\n   */\n  private calculateBalanceScore(ratios: CategoryRatios): number {\n    let score = 100;\n    \n    // 计算每个分类与理想比例的偏差\n    Object.keys(this.IDEAL_RATIOS).forEach(category => {\n      const ideal = this.IDEAL_RATIOS[category as keyof CategoryRatios];\n      const actual = ratios[category as keyof CategoryRatios];\n      const deviation = Math.abs(ideal - actual);\n      \n      // 偏差越大，扣分越多\n      score -= deviation * 100;\n    });\n    \n    return Math.max(0, Math.round(score));\n  }\n  \n  /**\n   * 生成个性化建议\n   */\n  private generateRecommendation(ratios: CategoryRatios, stats: DailyStats[]): string {\n    const recommendations: string[] = [];\n    \n    // 分析工作时间\n    if (ratios.work > 0.8) {\n      recommendations.push(\"⚠️ 工作时间过长，建议增加休息和娱乐时间\");\n    } else if (ratios.work < 0.3) {\n      recommendations.push(\"💼 工作时间较少，可以适当增加工作或学习时间\");\n    }\n    \n    // 分析提升时间\n    if (ratios.improvement < 0.1) {\n      recommendations.push(\"📚 建议安排一些学习或自我提升的活动\");\n    } else if (ratios.improvement > 0.4) {\n      recommendations.push(\"🎯 学习时间充足，注意劳逸结合\");\n    }\n    \n    // 分析娱乐时间\n    if (ratios.entertainment < 0.05) {\n      recommendations.push(\"🎮 需要更多的放松和娱乐时间\");\n    } else if (ratios.entertainment > 0.3) {\n      recommendations.push(\"⏰ 娱乐时间较多，可以适当增加工作或学习\");\n    }\n    \n    // 分析连续性\n    const recentDays = stats.slice(-3); // 最近3天\n    const hasConsistentWork = recentDays.every(day => day.workTime > 0);\n    const hasConsistentImprovement = recentDays.every(day => day.improvementTime > 0);\n    \n    if (!hasConsistentWork) {\n      recommendations.push(\"🔄 建议保持每日工作的连续性\");\n    }\n    \n    if (!hasConsistentImprovement) {\n      recommendations.push(\"📈 建议每天安排一些自我提升时间\");\n    }\n    \n    // 如果没有特别的建议，给出正面反馈\n    if (recommendations.length === 0) {\n      const score = this.calculateBalanceScore(ratios);\n      if (score >= 80) {\n        return \"✨ 生活平衡状态优秀，继续保持！\";\n      } else if (score >= 60) {\n        return \"👍 生活平衡状态良好，可以微调优化\";\n      } else {\n        return \"🎯 生活平衡有改善空间，建议关注时间分配\";\n      }\n    }\n    \n    return recommendations.join(\" • \");\n  }\n  \n  /**\n   * 获取用户的每日统计数据\n   */\n  private async getDailyStats(userId: string, days: number): Promise<DailyStats[]> {\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - days);\n    \n    const { data, error } = await supabase\n      .from('daily_stats')\n      .select('*')\n      .eq('user_id', userId)\n      .gte('date', startDate.toISOString().split('T')[0])\n      .order('date', { ascending: true });\n    \n    if (error) {\n      console.error('Error fetching daily stats:', error);\n      return [];\n    }\n    \n    return data.map(stat => ({\n      id: stat.id,\n      userId: stat.user_id,\n      date: new Date(stat.date),\n      workTime: stat.work_time,\n      improvementTime: stat.improvement_time,\n      entertainmentTime: stat.entertainment_time,\n      tasksCompleted: stat.tasks_completed,\n      tasksPostponed: stat.tasks_postponed,\n      balanceScore: stat.balance_score || 0\n    }));\n  }\n  \n  /**\n   * 更新今日统计数据\n   */\n  async updateTodayStats(userId: string, category: string, timeSpent: number): Promise<void> {\n    const today = new Date().toISOString().split('T')[0];\n    \n    try {\n      // 获取今日统计\n      const { data: existing } = await supabase\n        .from('daily_stats')\n        .select('*')\n        .eq('user_id', userId)\n        .eq('date', today)\n        .single();\n      \n      const updateData: any = {};\n      \n      if (category === 'work') {\n        updateData.work_time = (existing?.work_time || 0) + timeSpent;\n      } else if (category === 'improvement') {\n        updateData.improvement_time = (existing?.improvement_time || 0) + timeSpent;\n      } else if (category === 'entertainment') {\n        updateData.entertainment_time = (existing?.entertainment_time || 0) + timeSpent;\n      }\n      \n      if (existing) {\n        // 更新现有记录\n        await supabase\n          .from('daily_stats')\n          .update(updateData)\n          .eq('id', existing.id);\n      } else {\n        // 创建新记录\n        await supabase\n          .from('daily_stats')\n          .insert({\n            user_id: userId,\n            date: today,\n            ...updateData\n          });\n      }\n    } catch (error) {\n      console.error('Error updating daily stats:', error);\n    }\n  }\n  \n  /**\n   * 获取平衡状态的颜色指示\n   */\n  getBalanceStatusColor(score: number): string {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    if (score >= 40) return 'text-orange-600';\n    return 'text-red-600';\n  }\n  \n  /**\n   * 获取平衡状态的描述\n   */\n  getBalanceStatusText(score: number): string {\n    if (score >= 80) return '优秀';\n    if (score >= 60) return '良好';\n    if (score >= 40) return '一般';\n    return '需要改善';\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,YAAY;IACK,eAA+B;QAC9C,MAAM;QACN,aAAa;QACb,eAAe;IACjB,EAAE;IAEF;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAA4B;QACnE,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ;YAE/C,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,cAAc;YACd,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,MACnC,MAAM,IAAI,QAAQ,GAAG,IAAI,eAAe,GAAG,IAAI,iBAAiB,EAAE;YAGpE,IAAI,cAAc,GAAG;gBACnB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,aAAa;YACb,MAAM,SAAyB;gBAC7B,MAAM,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE,KAAK;gBAC1D,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,eAAe,EAAE,KAAK;gBACxE,eAAe,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,iBAAiB,EAAE,KAAK;YAC9E;YAEA,SAAS;YACT,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,OAAO;YACP,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YAE3D,OAAO;gBACL,WAAW,OAAO,IAAI;gBACtB,kBAAkB,OAAO,WAAW;gBACpC,oBAAoB,OAAO,aAAa;gBACxC;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAsB,EAAU;QAC5D,IAAI,QAAQ;QAEZ,iBAAiB;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YACrC,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAiC;YACjE,MAAM,SAAS,MAAM,CAAC,SAAiC;YACvD,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ;YAEnC,YAAY;YACZ,SAAS,YAAY;QACvB;QAEA,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC;IAChC;IAEA;;GAEC,GACD,AAAQ,uBAAuB,MAAsB,EAAE,KAAmB,EAAU;QAClF,MAAM,kBAA4B,EAAE;QAEpC,SAAS;QACT,IAAI,OAAO,IAAI,GAAG,KAAK;YACrB,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,WAAW,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK;YACnC,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,aAAa,GAAG,MAAM;YAC/B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,aAAa,GAAG,KAAK;YACrC,gBAAgB,IAAI,CAAC;QACvB;QAEA,QAAQ;QACR,MAAM,aAAa,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO;QAC3C,MAAM,oBAAoB,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG;QACjE,MAAM,2BAA2B,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,eAAe,GAAG;QAE/E,IAAI,CAAC,mBAAmB;YACtB,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,CAAC,0BAA0B;YAC7B,gBAAgB,IAAI,CAAC;QACvB;QAEA,mBAAmB;QACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,MAAM,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YACzC,IAAI,SAAS,IAAI;gBACf,OAAO;YACT,OAAO,IAAI,SAAS,IAAI;gBACtB,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,OAAO,gBAAgB,IAAI,CAAC;IAC9B;IAEA;;GAEC,GACD,MAAc,cAAc,MAAc,EAAE,IAAY,EAAyB;QAC/E,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,QAAQ,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACjD,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,UAAU,KAAK,SAAS;gBACxB,iBAAiB,KAAK,gBAAgB;gBACtC,mBAAmB,KAAK,kBAAkB;gBAC1C,gBAAgB,KAAK,eAAe;gBACpC,gBAAgB,KAAK,eAAe;gBACpC,cAAc,KAAK,aAAa,IAAI;YACtC,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAAE,QAAgB,EAAE,SAAiB,EAAiB;QACzF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAEpD,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ,OACX,MAAM;YAET,MAAM,aAAkB,CAAC;YAEzB,IAAI,aAAa,QAAQ;gBACvB,WAAW,SAAS,GAAG,CAAC,UAAU,aAAa,CAAC,IAAI;YACtD,OAAO,IAAI,aAAa,eAAe;gBACrC,WAAW,gBAAgB,GAAG,CAAC,UAAU,oBAAoB,CAAC,IAAI;YACpE,OAAO,IAAI,aAAa,iBAAiB;gBACvC,WAAW,kBAAkB,GAAG,CAAC,UAAU,sBAAsB,CAAC,IAAI;YACxE;YAEA,IAAI,UAAU;gBACZ,SAAS;gBACT,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SAAS,EAAE;YACzB,OAAO;gBACL,QAAQ;gBACR,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC;oBACN,SAAS;oBACT,MAAM;oBACN,GAAG,UAAU;gBACf;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAU;QAC3C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,KAAa,EAAU;QAC1C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/lib/algorithms/fix.ts"], "sourcesContent": ["import { Task, PostponedTaskAlert } from '@/types';\nimport { supabase } from '@/lib/supabase';\n\nexport class FixAlgorithm {\n  // 推迟次数阈值\n  private readonly POSTPONE_THRESHOLDS = {\n    low: 1,\n    medium: 3,\n    high: 5,\n    critical: 8\n  };\n  \n  /**\n   * 分析需要修复的推迟任务\n   */\n  async analyzePostponedTasks(userId: string): Promise<PostponedTaskAlert[]> {\n    try {\n      // 获取所有推迟的任务\n      const postponedTasks = await this.getPostponedTasks(userId);\n      \n      return postponedTasks.map(task => {\n        const urgencyLevel = this.calculateUrgencyLevel(task);\n        const suggestion = this.generateFixSuggestion(task);\n        const daysSinceCreated = this.calculateDaysSince(task.createdAt);\n        \n        return {\n          task,\n          postponeCount: task.postponeCount,\n          daysSinceCreated,\n          urgencyLevel,\n          suggestion,\n          shouldAlert: urgencyLevel !== 'low'\n        };\n      }).filter(alert => alert.shouldAlert); // 只返回需要提醒的任务\n    } catch (error) {\n      console.error('Error analyzing postponed tasks:', error);\n      return [];\n    }\n  }\n  \n  /**\n   * 计算任务的紧急程度\n   */\n  private calculateUrgencyLevel(task: Task): 'low' | 'medium' | 'high' | 'critical' {\n    const { postponeCount, deadline } = task;\n    const daysSinceDeadline = this.calculateDaysSince(deadline);\n    const daysSinceCreated = this.calculateDaysSince(task.createdAt);\n    \n    // 综合考虑推迟次数、截止时间和创建时间\n    let urgencyScore = 0;\n    \n    // 推迟次数评分\n    if (postponeCount >= this.POSTPONE_THRESHOLDS.critical) urgencyScore += 4;\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.high) urgencyScore += 3;\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.medium) urgencyScore += 2;\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.low) urgencyScore += 1;\n    \n    // 截止时间评分\n    if (daysSinceDeadline > 0) urgencyScore += 3; // 已过期\n    else if (daysSinceDeadline > -1) urgencyScore += 2; // 1天内到期\n    else if (daysSinceDeadline > -3) urgencyScore += 1; // 3天内到期\n    \n    // 创建时间评分（任务存在时间过长）\n    if (daysSinceCreated > 14) urgencyScore += 2; // 超过2周\n    else if (daysSinceCreated > 7) urgencyScore += 1; // 超过1周\n    \n    // 重要性和紧急性加权\n    if (task.importance >= 4) urgencyScore += 1;\n    if (task.urgency >= 4) urgencyScore += 1;\n    \n    // 根据总分确定紧急程度\n    if (urgencyScore >= 7) return 'critical';\n    if (urgencyScore >= 5) return 'high';\n    if (urgencyScore >= 3) return 'medium';\n    return 'low';\n  }\n  \n  /**\n   * 生成修复建议\n   */\n  private generateFixSuggestion(task: Task): string {\n    const urgencyLevel = this.calculateUrgencyLevel(task);\n    const { postponeCount, category, estimatedDuration } = task;\n    \n    // 基于紧急程度的基础建议\n    const baseSuggestions = {\n      critical: \"🚨 紧急处理：这个任务已经严重延期，建议立即处理或重新评估其必要性\",\n      high: \"⚠️ 重点关注：建议将任务分解为更小的部分，或调整截止时间\",\n      medium: \"💡 优化建议：可以设置更具体的时间安排或降低任务难度\",\n      low: \"📝 轻微提醒：建议适当调整任务优先级或时间安排\"\n    };\n    \n    let suggestion = baseSuggestions[urgencyLevel];\n    \n    // 基于推迟次数的具体建议\n    if (postponeCount >= 5) {\n      suggestion += \"\\n• 考虑将任务分解为5-10分钟的小任务\";\n    } else if (postponeCount >= 3) {\n      suggestion += \"\\n• 尝试番茄工作法，专注25分钟\";\n    }\n    \n    // 基于任务时长的建议\n    if (estimatedDuration > 120) { // 超过2小时\n      suggestion += \"\\n• 任务时间较长，建议分解为多个子任务\";\n    }\n    \n    // 基于分类的建议\n    if (category === 'work') {\n      suggestion += \"\\n• 工作任务：考虑在精力最好的时间段处理\";\n    } else if (category === 'improvement') {\n      suggestion += \"\\n• 提升任务：可以设置学习奖励机制\";\n    } else if (category === 'entertainment') {\n      suggestion += \"\\n• 娱乐任务：确保这确实是你想要的放松方式\";\n    }\n    \n    return suggestion;\n  }\n  \n  /**\n   * 获取推迟的任务\n   */\n  private async getPostponedTasks(userId: string): Promise<Task[]> {\n    const { data, error } = await supabase\n      .from('tasks')\n      .select('*')\n      .eq('user_id', userId)\n      .gt('postpone_count', 0)\n      .in('status', ['pending', 'postponed'])\n      .order('postpone_count', { ascending: false });\n    \n    if (error) {\n      console.error('Error fetching postponed tasks:', error);\n      return [];\n    }\n    \n    return data.map(task => ({\n      id: task.id,\n      userId: task.user_id,\n      title: task.title,\n      description: task.description,\n      category: task.category,\n      importance: task.importance,\n      urgency: task.urgency,\n      deadline: new Date(task.deadline),\n      estimatedDuration: task.estimated_duration,\n      status: task.status,\n      postponeCount: task.postpone_count,\n      createdAt: new Date(task.created_at),\n      updatedAt: new Date(task.updated_at)\n    }));\n  }\n  \n  /**\n   * 计算距离某个日期的天数\n   */\n  private calculateDaysSince(date: Date): number {\n    const now = new Date();\n    const diffTime = now.getTime() - date.getTime();\n    return Math.floor(diffTime / (1000 * 60 * 60 * 24));\n  }\n  \n  /**\n   * 推迟任务\n   */\n  async postponeTask(taskId: string, reason?: string): Promise<void> {\n    try {\n      // 获取当前任务\n      const { data: task, error: fetchError } = await supabase\n        .from('tasks')\n        .select('postpone_count')\n        .eq('id', taskId)\n        .single();\n      \n      if (fetchError) throw fetchError;\n      \n      // 更新推迟次数\n      const { error: updateError } = await supabase\n        .from('tasks')\n        .update({\n          postpone_count: (task.postpone_count || 0) + 1,\n          status: 'postponed',\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', taskId);\n      \n      if (updateError) throw updateError;\n      \n      // 记录推迟历史（如果有历史表的话）\n      // 这里可以扩展记录推迟原因等信息\n      \n    } catch (error) {\n      console.error('Error postponing task:', error);\n      throw new Error('Failed to postpone task');\n    }\n  }\n  \n  /**\n   * 重置任务的推迟状态\n   */\n  async resetTaskPostponeStatus(taskId: string): Promise<void> {\n    try {\n      const { error } = await supabase\n        .from('tasks')\n        .update({\n          postpone_count: 0,\n          status: 'pending',\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', taskId);\n      \n      if (error) throw error;\n    } catch (error) {\n      console.error('Error resetting task postpone status:', error);\n      throw new Error('Failed to reset task postpone status');\n    }\n  }\n  \n  /**\n   * 获取推迟统计信息\n   */\n  async getPostponeStats(userId: string): Promise<{\n    totalPostponedTasks: number;\n    averagePostponeCount: number;\n    mostPostponedCategory: string;\n  }> {\n    try {\n      const { data, error } = await supabase\n        .from('tasks')\n        .select('postpone_count, category')\n        .eq('user_id', userId)\n        .gt('postpone_count', 0);\n      \n      if (error) throw error;\n      \n      if (!data || data.length === 0) {\n        return {\n          totalPostponedTasks: 0,\n          averagePostponeCount: 0,\n          mostPostponedCategory: 'work'\n        };\n      }\n      \n      const totalPostponedTasks = data.length;\n      const averagePostponeCount = data.reduce((sum, task) => sum + task.postpone_count, 0) / totalPostponedTasks;\n      \n      // 统计各分类的推迟次数\n      const categoryStats = data.reduce((acc, task) => {\n        acc[task.category] = (acc[task.category] || 0) + task.postpone_count;\n        return acc;\n      }, {} as Record<string, number>);\n      \n      const mostPostponedCategory = Object.keys(categoryStats).reduce((a, b) => \n        categoryStats[a] > categoryStats[b] ? a : b\n      );\n      \n      return {\n        totalPostponedTasks,\n        averagePostponeCount: Math.round(averagePostponeCount * 10) / 10,\n        mostPostponedCategory\n      };\n    } catch (error) {\n      console.error('Error getting postpone stats:', error);\n      return {\n        totalPostponedTasks: 0,\n        averagePostponeCount: 0,\n        mostPostponedCategory: 'work'\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,SAAS;IACQ,sBAAsB;QACrC,KAAK;QACL,QAAQ;QACR,MAAM;QACN,UAAU;IACZ,EAAE;IAEF;;GAEC,GACD,MAAM,sBAAsB,MAAc,EAAiC;QACzE,IAAI;YACF,YAAY;YACZ,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAEpD,OAAO,eAAe,GAAG,CAAC,CAAA;gBACxB,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;gBAChD,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;gBAC9C,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;gBAE/D,OAAO;oBACL;oBACA,eAAe,KAAK,aAAa;oBACjC;oBACA;oBACA;oBACA,aAAa,iBAAiB;gBAChC;YACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,aAAa;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAA0C;QAChF,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG;QACpC,MAAM,oBAAoB,IAAI,CAAC,kBAAkB,CAAC;QAClD,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;QAE/D,qBAAqB;QACrB,IAAI,eAAe;QAEnB,SAAS;QACT,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,gBAAgB;aACnE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,gBAAgB;aACpE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,gBAAgB;aACtE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,gBAAgB;QAExE,SAAS;QACT,IAAI,oBAAoB,GAAG,gBAAgB,GAAG,MAAM;aAC/C,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;aACvD,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;QAE5D,mBAAmB;QACnB,IAAI,mBAAmB,IAAI,gBAAgB,GAAG,OAAO;aAChD,IAAI,mBAAmB,GAAG,gBAAgB,GAAG,OAAO;QAEzD,YAAY;QACZ,IAAI,KAAK,UAAU,IAAI,GAAG,gBAAgB;QAC1C,IAAI,KAAK,OAAO,IAAI,GAAG,gBAAgB;QAEvC,aAAa;QACb,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAAU;QAChD,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAChD,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG;QAEvD,cAAc;QACd,MAAM,kBAAkB;YACtB,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,IAAI,aAAa,eAAe,CAAC,aAAa;QAE9C,cAAc;QACd,IAAI,iBAAiB,GAAG;YACtB,cAAc;QAChB,OAAO,IAAI,iBAAiB,GAAG;YAC7B,cAAc;QAChB;QAEA,YAAY;QACZ,IAAI,oBAAoB,KAAK;YAC3B,cAAc;QAChB;QAEA,UAAU;QACV,IAAI,aAAa,QAAQ;YACvB,cAAc;QAChB,OAAO,IAAI,aAAa,eAAe;YACrC,cAAc;QAChB,OAAO,IAAI,aAAa,iBAAiB;YACvC,cAAc;QAChB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,kBAAkB,MAAc,EAAmB;QAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,UAAU;YAAC;YAAW;SAAY,EACrC,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,SAAS,KAAK,OAAO;gBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;gBAChC,mBAAmB,KAAK,kBAAkB;gBAC1C,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,cAAc;gBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;YACrC,CAAC;IACH;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAU;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,OAAO,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD;IAEA;;GAEC,GACD,MAAM,aAAa,MAAc,EAAE,MAAe,EAAiB;QACjE,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACrD,IAAI,CAAC,SACL,MAAM,CAAC,kBACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,YAAY,MAAM;YAEtB,SAAS;YACT,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI;gBAC7C,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,aAAa,MAAM;QAEvB,mBAAmB;QACnB,kBAAkB;QAEpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB,MAAc,EAAiB;QAC3D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB;gBAChB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAIlC;QACD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,4BACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB;YAExB,IAAI,OAAO,MAAM;YAEjB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO;oBACL,qBAAqB;oBACrB,sBAAsB;oBACtB,uBAAuB;gBACzB;YACF;YAEA,MAAM,sBAAsB,KAAK,MAAM;YACvC,MAAM,uBAAuB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE,KAAK;YAExF,aAAa;YACb,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,KAAK;gBACtC,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc;gBACpE,OAAO;YACT,GAAG,CAAC;YAEJ,MAAM,wBAAwB,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,CAAC,GAAG,IAClE,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,IAAI;YAG5C,OAAO;gBACL;gBACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,MAAM;gBAC9D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,qBAAqB;gBACrB,sBAAsB;gBACtB,uBAAuB;YACzB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/store/useTaskStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/types';\nimport { supabase } from '@/lib/supabase';\nimport { PlanningAlgorithm } from '@/lib/algorithms/planning';\nimport { BalanceAlgorithm } from '@/lib/algorithms/balance';\nimport { FixAlgorithm } from '@/lib/algorithms/fix';\n\ninterface TaskState {\n  // State\n  tasks: Task[];\n  dailySchedule: DailySchedule | null;\n  balanceAnalysis: BalanceAnalysis | null;\n  postponedAlerts: PostponedTaskAlert[];\n  loading: boolean;\n  error: string | null;\n  \n  // Algorithms\n  planningAlgorithm: PlanningAlgorithm;\n  balanceAlgorithm: BalanceAlgorithm;\n  fixAlgorithm: FixAlgorithm;\n  \n  // Actions\n  fetchTasks: (userId: string) => Promise<void>;\n  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;\n  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;\n  deleteTask: (id: string) => Promise<void>;\n  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;\n  postponeTask: (id: string, reason?: string) => Promise<void>;\n  \n  // Algorithm actions\n  generateDailySchedule: (userId: string) => Promise<void>;\n  analyzeBalance: (userId: string) => Promise<void>;\n  analyzePostponedTasks: (userId: string) => Promise<void>;\n  \n  // Utility actions\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n}\n\nexport const useTaskStore = create<TaskState>((set, get) => ({\n  // Initial state\n  tasks: [],\n  dailySchedule: null,\n  balanceAnalysis: null,\n  postponedAlerts: [],\n  loading: false,\n  error: null,\n  \n  // Algorithm instances\n  planningAlgorithm: new PlanningAlgorithm(),\n  balanceAlgorithm: new BalanceAlgorithm(),\n  fixAlgorithm: new FixAlgorithm(),\n  \n  // Fetch tasks\n  fetchTasks: async (userId: string) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { data, error } = await supabase\n        .from('tasks')\n        .select('*')\n        .eq('user_id', userId)\n        .order('created_at', { ascending: false });\n      \n      if (error) throw error;\n      \n      const tasks: Task[] = data.map(task => ({\n        id: task.id,\n        userId: task.user_id,\n        title: task.title,\n        description: task.description,\n        category: task.category,\n        importance: task.importance,\n        urgency: task.urgency,\n        deadline: new Date(task.deadline),\n        estimatedDuration: task.estimated_duration,\n        status: task.status,\n        postponeCount: task.postpone_count,\n        createdAt: new Date(task.created_at),\n        updatedAt: new Date(task.updated_at)\n      }));\n      \n      set({ tasks, loading: false });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';\n      set({ error: errorMessage, loading: false });\n    }\n  },\n  \n  // Create task\n  createTask: async (taskData) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { data, error } = await supabase\n        .from('tasks')\n        .insert({\n          user_id: taskData.userId,\n          title: taskData.title,\n          description: taskData.description,\n          category: taskData.category,\n          importance: taskData.importance,\n          urgency: taskData.urgency,\n          deadline: taskData.deadline.toISOString(),\n          estimated_duration: taskData.estimatedDuration,\n          status: taskData.status,\n          postpone_count: taskData.postponeCount\n        })\n        .select()\n        .single();\n      \n      if (error) throw error;\n      \n      const newTask: Task = {\n        id: data.id,\n        userId: data.user_id,\n        title: data.title,\n        description: data.description,\n        category: data.category,\n        importance: data.importance,\n        urgency: data.urgency,\n        deadline: new Date(data.deadline),\n        estimatedDuration: data.estimated_duration,\n        status: data.status,\n        postponeCount: data.postpone_count,\n        createdAt: new Date(data.created_at),\n        updatedAt: new Date(data.updated_at)\n      };\n      \n      set(state => ({\n        tasks: [newTask, ...state.tasks],\n        loading: false\n      }));\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';\n      set({ error: errorMessage, loading: false });\n      throw error;\n    }\n  },\n  \n  // Update task\n  updateTask: async (id: string, updates: Partial<Task>) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const updateData: any = {};\n      if (updates.title) updateData.title = updates.title;\n      if (updates.description !== undefined) updateData.description = updates.description;\n      if (updates.category) updateData.category = updates.category;\n      if (updates.importance) updateData.importance = updates.importance;\n      if (updates.urgency) updateData.urgency = updates.urgency;\n      if (updates.deadline) updateData.deadline = updates.deadline.toISOString();\n      if (updates.estimatedDuration) updateData.estimated_duration = updates.estimatedDuration;\n      if (updates.status) updateData.status = updates.status;\n      if (updates.postponeCount !== undefined) updateData.postpone_count = updates.postponeCount;\n      \n      updateData.updated_at = new Date().toISOString();\n      \n      const { error } = await supabase\n        .from('tasks')\n        .update(updateData)\n        .eq('id', id);\n      \n      if (error) throw error;\n      \n      set(state => ({\n        tasks: state.tasks.map(task => \n          task.id === id ? { ...task, ...updates, updatedAt: new Date() } : task\n        ),\n        loading: false\n      }));\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';\n      set({ error: errorMessage, loading: false });\n      throw error;\n    }\n  },\n  \n  // Delete task\n  deleteTask: async (id: string) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { error } = await supabase\n        .from('tasks')\n        .delete()\n        .eq('id', id);\n      \n      if (error) throw error;\n      \n      set(state => ({\n        tasks: state.tasks.filter(task => task.id !== id),\n        loading: false\n      }));\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';\n      set({ error: errorMessage, loading: false });\n      throw error;\n    }\n  },\n  \n  // Complete task\n  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {\n    try {\n      const { updateTask, balanceAlgorithm } = get();\n      const task = get().tasks.find(t => t.id === id);\n      \n      if (!task) throw new Error('Task not found');\n      \n      // Update task status\n      await updateTask(id, { status: 'completed' });\n      \n      // Record completion\n      if (actualDuration && satisfaction) {\n        await supabase\n          .from('task_completions')\n          .insert({\n            task_id: id,\n            actual_duration: actualDuration,\n            satisfaction_score: satisfaction\n          });\n      }\n      \n      // Update daily stats\n      await balanceAlgorithm.updateTodayStats(\n        task.userId, \n        task.category, \n        actualDuration || task.estimatedDuration\n      );\n      \n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';\n      set({ error: errorMessage });\n      throw error;\n    }\n  },\n  \n  // Postpone task\n  postponeTask: async (id: string, reason?: string) => {\n    try {\n      const { fixAlgorithm } = get();\n      await fixAlgorithm.postponeTask(id, reason);\n      \n      // Refresh tasks\n      const task = get().tasks.find(t => t.id === id);\n      if (task) {\n        await get().fetchTasks(task.userId);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';\n      set({ error: errorMessage });\n      throw error;\n    }\n  },\n  \n  // Generate daily schedule\n  generateDailySchedule: async (userId: string) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { tasks, planningAlgorithm } = get();\n      const userTasks = tasks.filter(task => task.userId === userId);\n      const schedule = planningAlgorithm.generateDailySchedule(userTasks);\n      \n      set({ dailySchedule: schedule, loading: false });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';\n      set({ error: errorMessage, loading: false });\n    }\n  },\n  \n  // Analyze balance\n  analyzeBalance: async (userId: string) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { balanceAlgorithm } = get();\n      const analysis = await balanceAlgorithm.analyzeWeeklyBalance(userId);\n      \n      set({ balanceAnalysis: analysis, loading: false });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';\n      set({ error: errorMessage, loading: false });\n    }\n  },\n  \n  // Analyze postponed tasks\n  analyzePostponedTasks: async (userId: string) => {\n    try {\n      set({ loading: true, error: null });\n      \n      const { fixAlgorithm } = get();\n      const alerts = await fixAlgorithm.analyzePostponedTasks(userId);\n      \n      set({ postponedAlerts: alerts, loading: false });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';\n      set({ error: errorMessage, loading: false });\n    }\n  },\n  \n  // Utility actions\n  setLoading: (loading: boolean) => set({ loading }),\n  setError: (error: string | null) => set({ error }),\n  clearError: () => set({ error: null })\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;;;;;;AAmCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,gBAAgB;QAChB,OAAO,EAAE;QACT,eAAe;QACf,iBAAiB;QACjB,iBAAiB,EAAE;QACnB,SAAS;QACT,OAAO;QAEP,sBAAsB;QACtB,mBAAmB,IAAI,uIAAA,CAAA,oBAAiB;QACxC,kBAAkB,IAAI,sIAAA,CAAA,mBAAgB;QACtC,cAAc,IAAI,kIAAA,CAAA,eAAY;QAE9B,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBAEjB,MAAM,QAAgB,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACtC,IAAI,KAAK,EAAE;wBACX,QAAQ,KAAK,OAAO;wBACpB,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,SAAS,KAAK,OAAO;wBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;wBAChC,mBAAmB,KAAK,kBAAkB;wBAC1C,QAAQ,KAAK,MAAM;wBACnB,eAAe,KAAK,cAAc;wBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;wBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACrC,CAAC;gBAED,IAAI;oBAAE;oBAAO,SAAS;gBAAM;YAC9B,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,SAAS,SAAS,MAAM;oBACxB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,SAAS,SAAS,OAAO;oBACzB,UAAU,SAAS,QAAQ,CAAC,WAAW;oBACvC,oBAAoB,SAAS,iBAAiB;oBAC9C,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,aAAa;gBACxC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,MAAM,UAAgB;oBACpB,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,SAAS,KAAK,OAAO;oBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;oBAChC,mBAAmB,KAAK,kBAAkB;oBAC1C,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,cAAc;oBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACrC;gBAEA,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO;4BAAC;+BAAY,MAAM,KAAK;yBAAC;wBAChC,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO,IAAY;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,aAAkB,CAAC;gBACzB,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,GAAG,QAAQ,KAAK;gBACnD,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;gBACnF,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBAC5D,IAAI,QAAQ,UAAU,EAAE,WAAW,UAAU,GAAG,QAAQ,UAAU;gBAClE,IAAI,QAAQ,OAAO,EAAE,WAAW,OAAO,GAAG,QAAQ,OAAO;gBACzD,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW;gBACxE,IAAI,QAAQ,iBAAiB,EAAE,WAAW,kBAAkB,GAAG,QAAQ,iBAAiB;gBACxF,IAAI,QAAQ,MAAM,EAAE,WAAW,MAAM,GAAG,QAAQ,MAAM;gBACtD,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,cAAc,GAAG,QAAQ,aAAa;gBAE1F,WAAW,UAAU,GAAG,IAAI,OAAO,WAAW;gBAE9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;wBAEpE,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;wBAC9C,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY,gBAAyB;YACxD,IAAI;gBACF,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG;gBACzC,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE5C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,qBAAqB;gBACrB,MAAM,WAAW,IAAI;oBAAE,QAAQ;gBAAY;gBAE3C,oBAAoB;gBACpB,IAAI,kBAAkB,cAAc;oBAClC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,oBACL,MAAM,CAAC;wBACN,SAAS;wBACT,iBAAiB;wBACjB,oBAAoB;oBACtB;gBACJ;gBAEA,qBAAqB;gBACrB,MAAM,iBAAiB,gBAAgB,CACrC,KAAK,MAAM,EACX,KAAK,QAAQ,EACb,kBAAkB,KAAK,iBAAiB;YAG5C,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY;YAC/B,IAAI;gBACF,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,aAAa,YAAY,CAAC,IAAI;gBAEpC,gBAAgB;gBAChB,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5C,IAAI,MAAM;oBACR,MAAM,MAAM,UAAU,CAAC,KAAK,MAAM;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG;gBACrC,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBACvD,MAAM,WAAW,kBAAkB,qBAAqB,CAAC;gBAEzD,IAAI;oBAAE,eAAe;oBAAU,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,WAAW,MAAM,iBAAiB,oBAAoB,CAAC;gBAE7D,IAAI;oBAAE,iBAAiB;oBAAU,SAAS;gBAAM;YAClD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,SAAS,MAAM,aAAa,qBAAqB,CAAC;gBAExD,IAAI;oBAAE,iBAAiB;oBAAQ,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC,UAAqB,IAAI;gBAAE;YAAQ;QAChD,UAAU,CAAC,QAAyB,IAAI;gBAAE;YAAM;QAChD,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useTaskStore } from '@/store/useTaskStore';\nimport { Calendar, Clock, Target, TrendingUp, Plus, Settings } from 'lucide-react';\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const { user, signOut } = useAuthStore();\n  const { \n    tasks, \n    dailySchedule, \n    balanceAnalysis, \n    postponedAlerts,\n    fetchTasks,\n    generateDailySchedule,\n    analyzeBalance,\n    analyzePostponedTasks,\n    loading\n  } = useTaskStore();\n\n  useEffect(() => {\n    if (!user) {\n      router.push('/auth/signin');\n      return;\n    }\n\n    // 初始化数据\n    const initializeData = async () => {\n      try {\n        await fetchTasks(user.id);\n        await generateDailySchedule(user.id);\n        await analyzeBalance(user.id);\n        await analyzePostponedTasks(user.id);\n      } catch (error) {\n        console.error('Failed to initialize dashboard data:', error);\n      }\n    };\n\n    initializeData();\n  }, [user, router, fetchTasks, generateDailySchedule, analyzeBalance, analyzePostponedTasks]);\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/auth/signin');\n    } catch (error) {\n      console.error('Sign out failed:', error);\n    }\n  };\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  const todayTasks = tasks.filter(task => {\n    const today = new Date();\n    const taskDate = new Date(task.deadline);\n    return taskDate.toDateString() === today.toDateString() && task.status !== 'completed';\n  });\n\n  const completedToday = tasks.filter(task => {\n    const today = new Date();\n    const taskDate = new Date(task.updatedAt);\n    return taskDate.toDateString() === today.toDateString() && task.status === 'completed';\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">TimeManager</h1>\n              <span className=\"ml-3 text-sm text-gray-500\">智能时间规划助手</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">欢迎，{user.email}</span>\n              <button\n                onClick={() => router.push('/settings')}\n                className=\"p-2 text-gray-400 hover:text-gray-600 rounded-md\"\n              >\n                <Settings className=\"h-5 w-5\" />\n              </button>\n              <button\n                onClick={handleSignOut}\n                className=\"text-sm text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md border border-gray-300 hover:bg-gray-50\"\n              >\n                退出登录\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Target className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">今日任务</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{todayTasks.length}</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Clock className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">已完成</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{completedToday.length}</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingUp className=\"h-8 w-8 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">生活平衡</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {balanceAnalysis?.balanceScore || 0}分\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Calendar className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">推迟提醒</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{postponedAlerts.length}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">快速操作</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <button\n                onClick={() => router.push('/tasks/new')}\n                className=\"flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-500 hover:bg-indigo-50 transition-colors\"\n              >\n                <Plus className=\"h-6 w-6 text-gray-400 mr-2\" />\n                <span className=\"text-gray-600\">添加新任务</span>\n              </button>\n              \n              <button\n                onClick={() => router.push('/planning')}\n                className=\"flex items-center justify-center p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                <Calendar className=\"h-6 w-6 mr-2\" />\n                <span>查看今日规划</span>\n              </button>\n              \n              <button\n                onClick={() => router.push('/tasks')}\n                className=\"flex items-center justify-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                <Target className=\"h-6 w-6 mr-2\" />\n                <span>管理所有任务</span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Today's Schedule Preview */}\n        {dailySchedule && dailySchedule.timeSlots.length > 0 && (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">今日规划预览</h2>\n              <div className=\"space-y-3\">\n                {dailySchedule.timeSlots.slice(0, 3).map((slot, index) => (\n                  <div key={index} className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex-shrink-0 w-20 text-sm text-gray-500\">\n                      {slot.startTime.toLocaleTimeString('zh-CN', { \n                        hour: '2-digit', \n                        minute: '2-digit' \n                      })}\n                    </div>\n                    <div className=\"ml-4 flex-1\">\n                      <p className=\"font-medium text-gray-900\">{slot.task.title}</p>\n                      <p className=\"text-sm text-gray-500\">{slot.task.category}</p>\n                    </div>\n                    <div className=\"flex-shrink-0\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {slot.task.estimatedDuration}分钟\n                      </span>\n                    </div>\n                  </div>\n                ))}\n                {dailySchedule.timeSlots.length > 3 && (\n                  <div className=\"text-center\">\n                    <button\n                      onClick={() => router.push('/planning')}\n                      className=\"text-indigo-600 hover:text-indigo-500 text-sm font-medium\"\n                    >\n                      查看完整规划 ({dailySchedule.timeSlots.length - 3} 个更多任务)\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Balance Analysis */}\n        {balanceAnalysis && (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">生活平衡分析</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                <div className=\"text-center\">\n                  <p className=\"text-sm text-gray-500\">工作</p>\n                  <p className=\"text-2xl font-semibold text-blue-600\">\n                    {Math.round(balanceAnalysis.workRatio * 100)}%\n                  </p>\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-sm text-gray-500\">提升</p>\n                  <p className=\"text-2xl font-semibold text-green-600\">\n                    {Math.round(balanceAnalysis.improvementRatio * 100)}%\n                  </p>\n                </div>\n                <div className=\"text-center\">\n                  <p className=\"text-sm text-gray-500\">娱乐</p>\n                  <p className=\"text-2xl font-semibold text-purple-600\">\n                    {Math.round(balanceAnalysis.entertainmentRatio * 100)}%\n                  </p>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 rounded-lg p-4\">\n                <p className=\"text-sm text-gray-700\">{balanceAnalysis.recommendation}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Postponed Tasks Alert */}\n        {postponedAlerts.length > 0 && (\n          <div className=\"mb-8\">\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n              <h2 className=\"text-lg font-semibold text-yellow-800 mb-4\">⚠️ 推迟任务提醒</h2>\n              <div className=\"space-y-3\">\n                {postponedAlerts.slice(0, 2).map((alert, index) => (\n                  <div key={index} className=\"bg-white rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{alert.task.title}</p>\n                        <p className=\"text-sm text-gray-500\">\n                          已推迟 {alert.postponeCount} 次 • {alert.daysSinceCreated} 天前创建\n                        </p>\n                      </div>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                        alert.urgencyLevel === 'critical' ? 'bg-red-100 text-red-800' :\n                        alert.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :\n                        'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {alert.urgencyLevel}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-2\">{alert.suggestion}</p>\n                  </div>\n                ))}\n                {postponedAlerts.length > 2 && (\n                  <div className=\"text-center\">\n                    <button\n                      onClick={() => router.push('/tasks?filter=postponed')}\n                      className=\"text-yellow-700 hover:text-yellow-600 text-sm font-medium\"\n                    >\n                      查看所有推迟任务 ({postponedAlerts.length - 2} 个更多)\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {loading && (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>\n            <p className=\"text-gray-500 mt-2\">正在加载数据...</p>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EACJ,KAAK,EACL,aAAa,EACb,eAAe,EACf,eAAe,EACf,UAAU,EACV,qBAAqB,EACrB,cAAc,EACd,qBAAqB,EACrB,OAAO,EACR,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,QAAQ;YACR,MAAM;sDAAiB;oBACrB,IAAI;wBACF,MAAM,WAAW,KAAK,EAAE;wBACxB,MAAM,sBAAsB,KAAK,EAAE;wBACnC,MAAM,eAAe,KAAK,EAAE;wBAC5B,MAAM,sBAAsB,KAAK,EAAE;oBACrC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wCAAwC;oBACxD;gBACF;;YAEA;QACF;8BAAG;QAAC;QAAM;QAAQ;QAAY;QAAuB;QAAgB;KAAsB;IAE3F,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA;QAC9B,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK,KAAK,QAAQ;QACvC,OAAO,SAAS,YAAY,OAAO,MAAM,YAAY,MAAM,KAAK,MAAM,KAAK;IAC7E;IAEA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA;QAClC,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;QACxC,OAAO,SAAS,YAAY,OAAO,MAAM,YAAY,MAAM,KAAK,MAAM,KAAK;IAC7E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAAI,KAAK,KAAK;;;;;;;kDACtD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAwC,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAK5E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAwC,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKhF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;wDACV,iBAAiB,gBAAgB;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAM5C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAwC,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAGlC,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOb,iBAAiB,cAAc,SAAS,CAAC,MAAM,GAAG,mBACjD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;wCACZ,cAAc,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC9C,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACZ,KAAK,SAAS,CAAC,kBAAkB,CAAC,SAAS;4DAC1C,MAAM;4DACN,QAAQ;wDACV;;;;;;kEAEF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA6B,KAAK,IAAI,CAAC,KAAK;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAyB,KAAK,IAAI,CAAC,QAAQ;;;;;;;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEACb,KAAK,IAAI,CAAC,iBAAiB;gEAAC;;;;;;;;;;;;;+CAbzB;;;;;wCAkBX,cAAc,SAAS,CAAC,MAAM,GAAG,mBAChC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;oDACX;oDACU,cAAc,SAAS,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUzD,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;;wDACV,KAAK,KAAK,CAAC,gBAAgB,SAAS,GAAG;wDAAK;;;;;;;;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;;wDACV,KAAK,KAAK,CAAC,gBAAgB,gBAAgB,GAAG;wDAAK;;;;;;;;;;;;;sDAGxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;;wDACV,KAAK,KAAK,CAAC,gBAAgB,kBAAkB,GAAG;wDAAK;;;;;;;;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAyB,gBAAgB,cAAc;;;;;;;;;;;;;;;;;;;;;;oBAO3E,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA6B,MAAM,IAAI,CAAC,KAAK;;;;;;kFAC1D,6LAAC;wEAAE,WAAU;;4EAAwB;4EAC9B,MAAM,aAAa;4EAAC;4EAAM,MAAM,gBAAgB;4EAAC;;;;;;;;;;;;;0EAG1D,6LAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,MAAM,YAAY,KAAK,aAAa,4BACpC,MAAM,YAAY,KAAK,SAAS,kCAChC,iCACA;0EACC,MAAM,YAAY;;;;;;;;;;;;kEAGvB,6LAAC;wDAAE,WAAU;kEAA8B,MAAM,UAAU;;;;;;;+CAhBnD;;;;;wCAmBX,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;oDACX;oDACY,gBAAgB,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASnD,yBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GAhTwB;;QACP,qIAAA,CAAA,YAAS;QACE,+HAAA,CAAA,eAAY;QAWlC,+HAAA,CAAA,eAAY;;;KAbM", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1938, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2082, "column": 0}, "map": {"version": 3, "file": "target.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/target.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "file": "settings.js", "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}