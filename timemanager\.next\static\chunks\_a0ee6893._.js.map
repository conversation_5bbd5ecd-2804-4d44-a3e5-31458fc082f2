{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { supabase } from '@/lib/supabase';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, loading } = useAuthStore();\n\n  useEffect(() => {\n    const handleRedirect = async () => {\n      console.log('🏠 Home page - checking auth state:', { user: user?.id, loading });\n\n      if (loading) {\n        console.log('⏳ Still loading, waiting...');\n        return;\n      }\n\n      if (user) {\n        console.log('👤 User logged in, checking onboarding status...');\n        try {\n          const { data: profile, error } = await supabase\n            .from('user_profiles')\n            .select('onboarding_completed')\n            .eq('id', user.id)\n            .single();\n\n          console.log('📋 Profile data:', profile);\n          console.log('❌ Profile error:', error);\n\n          if (error) {\n            console.log('🚀 Profile error, redirecting to onboarding...');\n            router.push('/onboarding');\n          } else if (profile?.onboarding_completed) {\n            console.log('✅ Onboarding completed, redirecting to dashboard...');\n            router.push('/dashboard');\n          } else {\n            console.log('📝 Onboarding not completed, redirecting to onboarding...');\n            router.push('/onboarding');\n          }\n        } catch (error) {\n          console.error('❌ Error checking profile:', error);\n          router.push('/onboarding');\n        }\n      } else {\n        console.log('🔐 No user, redirecting to signin...');\n        router.push('/auth/signin');\n      }\n    };\n\n    handleRedirect();\n  }, [user, loading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n        <p className=\"text-gray-600\">智能时间规划助手</p>\n        <p className=\"text-sm text-gray-500 mt-2\">正在检查登录状态...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;iDAAiB;oBACrB,QAAQ,GAAG,CAAC,uCAAuC;wBAAE,MAAM,MAAM;wBAAI;oBAAQ;oBAE7E,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC;wBACZ;oBACF;oBAEA,IAAI,MAAM;wBACR,QAAQ,GAAG,CAAC;wBACZ,IAAI;4BACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,iBACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;4BAET,QAAQ,GAAG,CAAC,oBAAoB;4BAChC,QAAQ,GAAG,CAAC,oBAAoB;4BAEhC,IAAI,OAAO;gCACT,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO,IAAI,SAAS,sBAAsB;gCACxC,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO;gCACL,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,OAAO,IAAI,CAAC;wBACd;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;GA1DwB;;QACP,qIAAA,CAAA,YAAS;QACE,+HAAA,CAAA,eAAY;;;KAFhB", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}