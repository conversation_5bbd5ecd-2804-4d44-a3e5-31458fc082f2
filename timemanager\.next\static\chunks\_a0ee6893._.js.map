{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { supabase } from '@/lib/supabase';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, setUser, loading, setLoading } = useAuthStore();\n  const [isMounted, setIsMounted] = useState(false);\n\n  // 防止水合错误\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!isMounted) return;\n\n    const handleRedirect = async () => {\n      console.log('🏠 Home page - checking auth state...');\n      console.log('👤 Current user from store:', user?.id || 'none');\n      console.log('⏳ Loading state:', loading);\n\n      // 如果正在加载，等待\n      if (loading) {\n        console.log('⏳ Still loading, waiting...');\n        return;\n      }\n\n      // 如果 store 中有用户，直接使用\n      if (user) {\n        console.log('✅ User found in store, checking onboarding...');\n        try {\n          const { data: profile, error: profileError } = await supabase\n            .from('user_profiles')\n            .select('onboarding_completed')\n            .eq('id', user.id)\n            .single();\n\n          console.log('📋 Profile data:', profile);\n\n          if (profileError) {\n            console.log('🚀 Profile error, redirecting to onboarding...');\n            router.push('/onboarding');\n          } else if (profile?.onboarding_completed) {\n            console.log('✅ Onboarding completed, redirecting to dashboard...');\n            router.push('/dashboard');\n          } else {\n            console.log('📝 Onboarding not completed, redirecting to onboarding...');\n            router.push('/onboarding');\n          }\n        } catch (error) {\n          console.error('❌ Error checking profile:', error);\n          router.push('/onboarding');\n        }\n      } else {\n        // 如果 store 中没有用户，尝试从 Supabase 获取\n        console.log('🔍 No user in store, checking Supabase...');\n        try {\n          setLoading(true);\n          const { data: { user: currentUser }, error } = await supabase.auth.getUser();\n\n          console.log('👤 Supabase user:', currentUser?.id || 'none');\n\n          if (error || !currentUser) {\n            console.log('🔐 No user found, redirecting to signin...');\n            router.push('/auth/signin');\n          } else {\n            // 用户存在，更新 store 并检查 onboarding\n            setUser(currentUser);\n            console.log('✅ User restored to store, will recheck...');\n          }\n        } catch (error) {\n          console.error('❌ Error checking Supabase auth:', error);\n          router.push('/auth/signin');\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    handleRedirect();\n  }, [isMounted, user, loading, router, setUser, setLoading]);\n\n  // 防止水合错误：在服务端和客户端挂载前显示相同内容\n  if (!isMounted || loading) {\n\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n          <p className=\"text-gray-600\">智能时间规划助手</p>\n          <p className=\"text-sm text-gray-500 mt-2\">正在检查登录状态...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // 这里不应该到达，因为上面的 useEffect 会处理重定向\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;QACf;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM;iDAAiB;oBACrB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,+BAA+B,MAAM,MAAM;oBACvD,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,YAAY;oBACZ,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC;wBACZ;oBACF;oBAEA,qBAAqB;oBACrB,IAAI,MAAM;wBACR,QAAQ,GAAG,CAAC;wBACZ,IAAI;4BACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,iBACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;4BAET,QAAQ,GAAG,CAAC,oBAAoB;4BAEhC,IAAI,cAAc;gCAChB,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO,IAAI,SAAS,sBAAsB;gCACxC,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO;gCACL,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,OAAO,IAAI,CAAC;wBACd;oBACF,OAAO;wBACL,iCAAiC;wBACjC,QAAQ,GAAG,CAAC;wBACZ,IAAI;4BACF,WAAW;4BACX,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;4BAE1E,QAAQ,GAAG,CAAC,qBAAqB,aAAa,MAAM;4BAEpD,IAAI,SAAS,CAAC,aAAa;gCACzB,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd,OAAO;gCACL,+BAA+B;gCAC/B,QAAQ;gCACR,QAAQ,GAAG,CAAC;4BACd;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,mCAAmC;4BACjD,OAAO,IAAI,CAAC;wBACd,SAAU;4BACR,WAAW;wBACb;oBACF;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAW;QAAM;QAAS;QAAQ;QAAS;KAAW;IAE1D,2BAA2B;IAC3B,IAAI,CAAC,aAAa,SAAS;QAEzB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,iCAAiC;IACjC,OAAO;AACT;GAhGwB;;QACP,qIAAA,CAAA,YAAS;QACuB,+HAAA,CAAA,eAAY;;;KAFrC", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}