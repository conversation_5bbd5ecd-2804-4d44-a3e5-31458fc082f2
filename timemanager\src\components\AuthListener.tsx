'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';

interface AuthListenerProps {
  children: React.ReactNode;
}

export default function AuthListener({ children }: AuthListenerProps) {
  const { setUser } = useAuthStore();
  const [isMounted, setIsMounted] = useState(false);

  // 防止水合错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    console.log('🎧 AuthListener: Setting up auth state listener...');

    // 监听 Supabase 认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.id || 'none');
        
        // 同步认证状态到 Zustand store
        setUser(session?.user || null);
        
        // 记录状态变化
        if (event === 'SIGNED_IN') {
          console.log('✅ User signed in:', session?.user?.email);
        } else if (event === 'SIGNED_OUT') {
          console.log('👋 User signed out');
        } else if (event === 'TOKEN_REFRESHED') {
          console.log('🔄 Token refreshed for:', session?.user?.email);
        }
      }
    );

    return () => {
      console.log('🎧 AuthListener: Cleaning up auth listener...');
      subscription.unsubscribe();
    };
  }, [isMounted, setUser]);

  // 在客户端挂载前不渲染任何内容，避免水合错误
  if (!isMounted) {
    return null;
  }

  return <>{children}</>;
}
