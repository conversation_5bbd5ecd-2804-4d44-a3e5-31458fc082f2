{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, loading } = useAuthStore();\n\n  useEffect(() => {\n    // AuthProvider 已经处理了认证逻辑，这里只需要简单重定向\n    if (!loading) {\n      if (user) {\n        // 用户已登录，重定向到 dashboard（AuthProvider 会处理 onboarding 检查）\n        router.push('/dashboard');\n      } else {\n        // 用户未登录，重定向到登录页\n        router.push('/auth/signin');\n      }\n    }\n  }, [user, loading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n        <p className=\"text-gray-600\">智能时间规划助手</p>\n        <p className=\"text-sm text-gray-500 mt-2\">正在检查登录状态...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,oCAAoC;YACpC,IAAI,CAAC,SAAS;gBACZ,IAAI,MAAM;oBACR,uDAAuD;oBACvD,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,gBAAgB;oBAChB,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;GA3BwB;;QACP,qIAAA,CAAA,YAAS;QACE,+HAAA,CAAA,eAAY;;;KAFhB", "debugId": null}}]}