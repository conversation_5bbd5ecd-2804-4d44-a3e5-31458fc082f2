'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { auth, supabase } from '@/lib/supabase';

interface AuthProviderProps {
  children: React.ReactNode;
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, setUser, setLoading } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // 公开路由，不需要认证
  const publicRoutes = ['/auth/signin', '/auth/signup'];
  const isPublicRoute = publicRoutes.includes(pathname);

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        setLoading(true);
        
        // 从 Supabase 获取当前用户状态
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();
        
        if (error) {
          console.error('Auth initialization error:', error);
          if (mounted) {
            setUser(null);
            if (!isPublicRoute) {
              router.push('/auth/signin');
            }
          }
          return;
        }

        if (mounted) {
          setUser(currentUser);
          
          if (currentUser) {
            // 用户已登录，检查是否需要重定向
            if (isPublicRoute) {
              // 如果在登录页面但已登录，检查是否完成onboarding
              try {
                const { data: profile } = await supabase
                  .from('user_profiles')
                  .select('onboarding_completed')
                  .eq('id', currentUser.id)
                  .single();

                if (profile?.onboarding_completed) {
                  router.push('/dashboard');
                } else {
                  router.push('/onboarding');
                }
              } catch (profileError) {
                console.error('Profile check error:', profileError);
                router.push('/onboarding');
              }
            }
          } else {
            // 用户未登录
            if (!isPublicRoute) {
              router.push('/auth/signin');
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        if (mounted) {
          setUser(null);
          if (!isPublicRoute) {
            router.push('/auth/signin');
          }
        }
      } finally {
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
        }
      }
    };

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        if (mounted) {
          setUser(session?.user || null);
          
          if (event === 'SIGNED_OUT' || !session?.user) {
            // 用户登出或会话失效
            if (!isPublicRoute) {
              router.push('/auth/signin');
            }
          } else if (event === 'SIGNED_IN' && session?.user) {
            // 用户登录成功
            try {
              const { data: profile } = await supabase
                .from('user_profiles')
                .select('onboarding_completed')
                .eq('id', session.user.id)
                .single();

              if (profile?.onboarding_completed) {
                router.push('/dashboard');
              } else {
                router.push('/onboarding');
              }
            } catch (profileError) {
              console.error('Profile check error:', profileError);
              router.push('/onboarding');
            }
          }
        }
      }
    );

    // 初始化认证状态
    initializeAuth();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [pathname, router, setUser, setLoading, isPublicRoute]);

  // 在初始化完成前显示加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">TimeManager</h1>
          <p className="text-gray-600">智能时间规划助手</p>
          <p className="text-sm text-gray-500 mt-2">正在初始化...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
