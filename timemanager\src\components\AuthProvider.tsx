'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';

interface AuthProviderProps {
  children: React.ReactNode;
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const { setUser, setLoading } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      console.log('🔄 Initializing auth...');

      try {
        setLoading(true);

        // 从 Supabase 获取当前用户状态
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();

        console.log('👤 Current user:', currentUser?.id || 'none');
        console.log('❌ Auth error:', error?.message || 'none');

        if (mounted) {
          setUser(currentUser);
          console.log('✅ Auth initialized successfully');
        }
      } catch (error) {
        console.error('❌ Auth initialization failed:', error);
        if (mounted) {
          setUser(null);
        }
      } finally {
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
          console.log('🎯 Auth initialization complete');
        }
      }
    };

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.id || 'none');

        if (mounted) {
          setUser(session?.user || null);
        }
      }
    );

    // 初始化认证状态
    initializeAuth();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []); // 空依赖数组，只在组件挂载时运行一次

  // 在初始化完成前显示加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">TimeManager</h1>
          <p className="text-gray-600">智能时间规划助手</p>
          <p className="text-sm text-gray-500 mt-2">正在初始化认证状态...</p>
          <p className="text-xs text-gray-400 mt-1">请查看控制台日志</p>
        </div>
      </div>
    );
  }

  console.log('🎉 AuthProvider initialized, rendering children');
  return <>{children}</>;
}
