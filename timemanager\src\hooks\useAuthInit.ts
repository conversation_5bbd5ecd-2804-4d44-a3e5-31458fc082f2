'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';

export function useAuthInit() {
  const { user, setUser, setLoading } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      console.log('🔧 useAuthInit: Initializing auth state...');
      
      try {
        setLoading(true);
        
        // 首先检查 localStorage 中是否有持久化的用户数据
        const storedAuth = localStorage.getItem('timemanager-auth');
        if (storedAuth) {
          try {
            const parsed = JSON.parse(storedAuth);
            console.log('💾 Found stored auth data:', parsed.state?.user?.id || 'none');
          } catch (e) {
            console.log('❌ Error parsing stored auth data');
          }
        }

        // 从 Supabase 获取当前认证状态
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();
        
        console.log('👤 Supabase current user:', currentUser?.id || 'none');
        console.log('❌ Supabase error:', error?.message || 'none');
        
        if (mounted) {
          if (currentUser) {
            setUser(currentUser);
            console.log('✅ Auth initialized with user:', currentUser.email);
          } else {
            setUser(null);
            console.log('🔐 Auth initialized without user');
          }
        }
      } catch (error) {
        console.error('❌ Auth initialization failed:', error);
        if (mounted) {
          setUser(null);
        }
      } finally {
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
          console.log('🎯 Auth initialization complete');
        }
      }
    };

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, [setUser, setLoading]);

  return { isInitialized, user };
}
