{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// 检查是否配置了有效的Supabase环境变量\nconst isSupabaseConfigured = supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_url_here' &&\n  supabaseAnonKey !== 'your_supabase_anon_key_here' &&\n  supabaseUrl.startsWith('https://');\n\nlet supabaseClient: any = null;\n\nif (isSupabaseConfigured) {\n  supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    realtime: {\n      params: {\n        eventsPerSecond: 10\n      }\n    }\n  });\n} else {\n  console.warn('⚠️ Supabase not configured. Running in development mode without database.');\n  // 创建一个模拟的Supabase客户端用于开发\n  supabaseClient = {\n    auth: {\n      signUp: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      signInWithPassword: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      signOut: () => Promise.resolve({ error: null }),\n      getUser: () => Promise.resolve({ data: { user: null }, error: null }),\n      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })\n    },\n    from: () => ({\n      select: () => ({ eq: () => ({ order: () => Promise.resolve({ data: [], error: null }) }) }),\n      insert: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      update: () => Promise.resolve({ error: new Error('Supabase not configured') }),\n      delete: () => Promise.resolve({ error: new Error('Supabase not configured') })\n    })\n  };\n}\n\nexport const supabase = supabaseClient;\n\n// 数据库类型定义\nexport interface Database {\n  public: {\n    Tables: {\n      user_profiles: {\n        Row: {\n          id: string;\n          email: string;\n          name: string | null;\n          timezone: string;\n          work_hours: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config: any; // 详细的时间配置\n          onboarding_completed: boolean;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id: string;\n          email: string;\n          name?: string | null;\n          timezone?: string;\n          work_hours?: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios?: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config?: any;\n          onboarding_completed?: boolean;\n        };\n        Update: {\n          name?: string | null;\n          timezone?: string;\n          work_hours?: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios?: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config?: any;\n          onboarding_completed?: boolean;\n        };\n      };\n      tasks: {\n        Row: {\n          id: string;\n          user_id: string;\n          title: string;\n          description: string | null;\n          category: 'work' | 'improvement' | 'entertainment';\n          importance: number;\n          urgency: number;\n          deadline: string;\n          estimated_duration: number;\n          status: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count: number;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          user_id: string;\n          title: string;\n          description?: string | null;\n          category: 'work' | 'improvement' | 'entertainment';\n          importance: number;\n          urgency: number;\n          deadline: string;\n          estimated_duration: number;\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count?: number;\n        };\n        Update: {\n          title?: string;\n          description?: string | null;\n          category?: 'work' | 'improvement' | 'entertainment';\n          importance?: number;\n          urgency?: number;\n          deadline?: string;\n          estimated_duration?: number;\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count?: number;\n        };\n      };\n      task_completions: {\n        Row: {\n          id: string;\n          task_id: string;\n          completed_at: string;\n          actual_duration: number;\n          satisfaction_score: number;\n        };\n        Insert: {\n          task_id: string;\n          actual_duration: number;\n          satisfaction_score: number;\n        };\n        Update: {\n          actual_duration?: number;\n          satisfaction_score?: number;\n        };\n      };\n      daily_stats: {\n        Row: {\n          id: string;\n          user_id: string;\n          date: string;\n          work_time: number;\n          improvement_time: number;\n          entertainment_time: number;\n          tasks_completed: number;\n          tasks_postponed: number;\n          balance_score: number | null;\n        };\n        Insert: {\n          user_id: string;\n          date: string;\n          work_time?: number;\n          improvement_time?: number;\n          entertainment_time?: number;\n          tasks_completed?: number;\n          tasks_postponed?: number;\n          balance_score?: number | null;\n        };\n        Update: {\n          work_time?: number;\n          improvement_time?: number;\n          entertainment_time?: number;\n          tasks_completed?: number;\n          tasks_postponed?: number;\n          balance_score?: number | null;\n        };\n      };\n    };\n  };\n}\n\n// 认证相关工具函数\nexport const auth = {\n  signUp: async (email: string, password: string) => {\n    return await supabase.auth.signUp({ email, password });\n  },\n  \n  signIn: async (email: string, password: string) => {\n    return await supabase.auth.signInWithPassword({ email, password });\n  },\n  \n  signOut: async () => {\n    return await supabase.auth.signOut();\n  },\n  \n  getCurrentUser: async () => {\n    const { data: { user } } = await supabase.auth.getUser();\n    return user;\n  },\n  \n  onAuthStateChange: (callback: (event: string, session: any) => void) => {\n    return supabase.auth.onAuthStateChange(callback);\n  }\n};\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEN,yBAAyB;AACzB,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,4BAChB,oBAAoB,iCACpB,YAAY,UAAU,CAAC;AAEzB,IAAI,iBAAsB;AAE1B,IAAI,sBAAsB;IACxB,iBAAiB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAc,iBAAkB;QAC5D,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;QACA,UAAU;YACR,QAAQ;gBACN,iBAAiB;YACnB;QACF;IACF;AACF,OAAO;IACL,QAAQ,IAAI,CAAC;IACb,yBAAyB;IACzB,iBAAiB;QACf,MAAM;YACJ,QAAQ,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACxF,oBAAoB,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACpG,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;YAC7C,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;gBAAK;YACnE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;QACjF;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBAAE,IAAI,IAAM,CAAC;gCAAE,OAAO,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM,EAAE;wCAAE,OAAO;oCAAK;4BAAG,CAAC;oBAAE,CAAC;gBAC1F,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;gBAC5E,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YAC9E,CAAC;IACH;AACF;AAEO,MAAM,WAAW;AA4JjB,MAAM,OAAO;IAClB,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAAE;YAAO;QAAS;IACtD;IAEA,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;IAClE;IAEA,SAAS;QACP,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;IACpC;IAEA,gBAAgB;QACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,mBAAmB,CAAC;QAClB,OAAO,SAAS,IAAI,CAAC,iBAAiB,CAAC;IACzC;AACF", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User } from '@supabase/supabase-js';\nimport { auth } from '@/lib/supabase';\n\ninterface AuthState {\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  \n  // Actions\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  signOut: () => Promise<void>;\n  setUser: (user: User | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      loading: false,\n      error: null,\n      \n      signIn: async (email: string, password: string) => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { data, error } = await auth.signIn(email, password);\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          if (data.user) {\n            set({ user: data.user, loading: false });\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign in failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      signUp: async (email: string, password: string) => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { data, error } = await auth.signUp(email, password);\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          if (data.user) {\n            set({ user: data.user, loading: false });\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign up failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      signOut: async () => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { error } = await auth.signOut();\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          set({ user: null, loading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign out failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      setUser: (user: User | null) => {\n        set({ user });\n      },\n      \n      setLoading: (loading: boolean) => {\n        set({ loading });\n      },\n      \n      setError: (error: string | null) => {\n        set({ error });\n      },\n      \n      clearError: () => {\n        set({ error: null });\n      }\n    }),\n    {\n      name: 'timemanager-auth',\n      partialize: (state) => ({\n        user: state.user\n      }),\n      // 使用 localStorage 持久化\n      storage: {\n        getItem: (name) => {\n          if (typeof window !== 'undefined') {\n            const value = localStorage.getItem(name);\n            return value ? JSON.parse(value) : null;\n          }\n          return null;\n        },\n        setItem: (name, value) => {\n          if (typeof window !== 'undefined') {\n            localStorage.setItem(name, JSON.stringify(value));\n          }\n        },\n        removeItem: (name) => {\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem(name);\n          }\n        },\n      },\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,OAAO;QAEP,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS;YACP,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;gBAEpC,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI;oBAAE,MAAM;oBAAM,SAAS;gBAAM;YACnC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;IACD,sBAAsB;IACtB,SAAS;QACP,SAAS,CAAC;YACR,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS;YACrC;;QAEF;QACA,SAAS,CAAC,MAAM;YACd,wCAAmC;gBACjC,aAAa,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC;YAC5C;QACF;QACA,YAAY,CAAC;YACX,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { Home, ArrowLeft, Search } from 'lucide-react';\n\nexport default function NotFound() {\n  const router = useRouter();\n  const { user } = useAuthStore();\n  const [isMounted, setIsMounted] = useState(false);\n\n  // 防止水合错误\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // 确保认证状态在404页面也能正确显示\n  useEffect(() => {\n    if (isMounted) {\n      console.log('404 page loaded, user state:', user?.id || 'not logged in');\n    }\n  }, [user, isMounted]);\n\n  const handleGoHome = () => {\n    if (isMounted && user) {\n      router.push('/dashboard');\n    } else {\n      router.push('/auth/signin');\n    }\n  };\n\n  const handleGoBack = () => {\n    router.back();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          {/* 404 图标 */}\n          <div className=\"mb-6\">\n            <div className=\"mx-auto w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center\">\n              <Search className=\"w-12 h-12 text-indigo-600\" />\n            </div>\n          </div>\n\n          {/* 错误信息 */}\n          <h1 className=\"text-6xl font-bold text-gray-900 mb-4\">404</h1>\n          <h2 className=\"text-2xl font-semibold text-gray-800 mb-2\">页面未找到</h2>\n          <p className=\"text-gray-600 mb-8\">\n            抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。\n          </p>\n\n          {/* 用户状态信息 - 只在客户端挂载后显示 */}\n          {isMounted && user && (\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-3 mb-6\">\n              <p className=\"text-sm text-green-700\">\n                ✅ 您仍处于登录状态 ({user.email})\n              </p>\n            </div>\n          )}\n\n          {/* 操作按钮 */}\n          <div className=\"space-y-3\">\n            <button\n              onClick={handleGoHome}\n              className=\"w-full flex items-center justify-center px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n            >\n              <Home className=\"w-5 h-5 mr-2\" />\n              {isMounted && user ? '返回控制台' : '去登录'}\n            </button>\n\n            <button\n              onClick={handleGoBack}\n              className=\"w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\"\n            >\n              <ArrowLeft className=\"w-5 h-5 mr-2\" />\n              返回上一页\n            </button>\n          </div>\n\n          {/* 帮助信息 */}\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <p className=\"text-sm text-gray-500\">\n              如果您认为这是一个错误，请联系技术支持\n            </p>\n          </div>\n        </div>\n\n        {/* 底部信息 */}\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-500\">\n            TimeManager - 智能时间规划助手\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa;QACf;6BAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,gCAAgC,MAAM,MAAM;YAC1D;QACF;6BAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,eAAe;QACnB,IAAI,aAAa,MAAM;YACrB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAKtB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAKjC,aAAa,sBACZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAyB;oCACvB,KAAK,KAAK;oCAAC;;;;;;;;;;;;sCAM9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,aAAa,OAAO,UAAU;;;;;;;8CAGjC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAM1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;GA5FwB;;QACP,qIAAA,CAAA,YAAS;QACP,+HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}]}