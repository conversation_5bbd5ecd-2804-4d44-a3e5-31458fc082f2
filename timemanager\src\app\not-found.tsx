'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { Home, ArrowLeft, Search } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();
  const { user } = useAuthStore();

  // 确保认证状态在404页面也能正确显示
  useEffect(() => {
    console.log('404 page loaded, user state:', user?.id || 'not logged in');
  }, [user]);

  const handleGoHome = () => {
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/auth/signin');
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* 404 图标 */}
          <div className="mb-6">
            <div className="mx-auto w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center">
              <Search className="w-12 h-12 text-indigo-600" />
            </div>
          </div>

          {/* 错误信息 */}
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">页面未找到</h2>
          <p className="text-gray-600 mb-8">
            抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
          </p>

          {/* 用户状态信息 */}
          {user && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-green-700">
                ✅ 您仍处于登录状态 ({user.email})
              </p>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="space-y-3">
            <button
              onClick={handleGoHome}
              className="w-full flex items-center justify-center px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <Home className="w-5 h-5 mr-2" />
              {user ? '返回控制台' : '去登录'}
            </button>

            <button
              onClick={handleGoBack}
              className="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回上一页
            </button>
          </div>

          {/* 帮助信息 */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              如果您认为这是一个错误，请联系技术支持
            </p>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            TimeManager - 智能时间规划助手
          </p>
        </div>
      </div>
    </div>
  );
}
