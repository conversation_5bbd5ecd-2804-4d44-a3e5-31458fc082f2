{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// 检查是否配置了有效的Supabase环境变量\nconst isSupabaseConfigured = supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_url_here' &&\n  supabaseAnonKey !== 'your_supabase_anon_key_here' &&\n  supabaseUrl.startsWith('https://');\n\nlet supabaseClient: any = null;\n\nif (isSupabaseConfigured) {\n  supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    realtime: {\n      params: {\n        eventsPerSecond: 10\n      }\n    }\n  });\n} else {\n  console.warn('⚠️ Supabase not configured. Running in development mode without database.');\n  // 创建一个模拟的Supabase客户端用于开发\n  supabaseClient = {\n    auth: {\n      signUp: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      signInWithPassword: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      signOut: () => Promise.resolve({ error: null }),\n      getUser: () => Promise.resolve({ data: { user: null }, error: null }),\n      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })\n    },\n    from: () => ({\n      select: () => ({ eq: () => ({ order: () => Promise.resolve({ data: [], error: null }) }) }),\n      insert: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n      update: () => Promise.resolve({ error: new Error('Supabase not configured') }),\n      delete: () => Promise.resolve({ error: new Error('Supabase not configured') })\n    })\n  };\n}\n\nexport const supabase = supabaseClient;\n\n// 数据库类型定义\nexport interface Database {\n  public: {\n    Tables: {\n      user_profiles: {\n        Row: {\n          id: string;\n          email: string;\n          name: string | null;\n          timezone: string;\n          work_hours: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config: any; // 详细的时间配置\n          onboarding_completed: boolean;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id: string;\n          email: string;\n          name?: string | null;\n          timezone?: string;\n          work_hours?: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios?: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config?: any;\n          onboarding_completed?: boolean;\n        };\n        Update: {\n          name?: string | null;\n          timezone?: string;\n          work_hours?: {\n            start: string;\n            end: string;\n            days?: string[];\n          };\n          category_ratios?: {\n            work: number;\n            improvement: number;\n            entertainment: number;\n          };\n          time_config?: any;\n          onboarding_completed?: boolean;\n        };\n      };\n      tasks: {\n        Row: {\n          id: string;\n          user_id: string;\n          title: string;\n          description: string | null;\n          category: 'work' | 'improvement' | 'entertainment';\n          importance: number;\n          urgency: number;\n          deadline: string;\n          estimated_duration: number;\n          status: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count: number;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          user_id: string;\n          title: string;\n          description?: string | null;\n          category: 'work' | 'improvement' | 'entertainment';\n          importance: number;\n          urgency: number;\n          deadline: string;\n          estimated_duration: number;\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count?: number;\n        };\n        Update: {\n          title?: string;\n          description?: string | null;\n          category?: 'work' | 'improvement' | 'entertainment';\n          importance?: number;\n          urgency?: number;\n          deadline?: string;\n          estimated_duration?: number;\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\n          postpone_count?: number;\n        };\n      };\n      task_completions: {\n        Row: {\n          id: string;\n          task_id: string;\n          completed_at: string;\n          actual_duration: number;\n          satisfaction_score: number;\n        };\n        Insert: {\n          task_id: string;\n          actual_duration: number;\n          satisfaction_score: number;\n        };\n        Update: {\n          actual_duration?: number;\n          satisfaction_score?: number;\n        };\n      };\n      daily_stats: {\n        Row: {\n          id: string;\n          user_id: string;\n          date: string;\n          work_time: number;\n          improvement_time: number;\n          entertainment_time: number;\n          tasks_completed: number;\n          tasks_postponed: number;\n          balance_score: number | null;\n        };\n        Insert: {\n          user_id: string;\n          date: string;\n          work_time?: number;\n          improvement_time?: number;\n          entertainment_time?: number;\n          tasks_completed?: number;\n          tasks_postponed?: number;\n          balance_score?: number | null;\n        };\n        Update: {\n          work_time?: number;\n          improvement_time?: number;\n          entertainment_time?: number;\n          tasks_completed?: number;\n          tasks_postponed?: number;\n          balance_score?: number | null;\n        };\n      };\n    };\n  };\n}\n\n// 认证相关工具函数\nexport const auth = {\n  signUp: async (email: string, password: string) => {\n    return await supabase.auth.signUp({ email, password });\n  },\n  \n  signIn: async (email: string, password: string) => {\n    return await supabase.auth.signInWithPassword({ email, password });\n  },\n  \n  signOut: async () => {\n    return await supabase.auth.signOut();\n  },\n  \n  getCurrentUser: async () => {\n    const { data: { user } } = await supabase.auth.getUser();\n    return user;\n  },\n  \n  onAuthStateChange: (callback: (event: string, session: any) => void) => {\n    return supabase.auth.onAuthStateChange(callback);\n  }\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEN,yBAAyB;AACzB,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,4BAChB,oBAAoB,iCACpB,YAAY,UAAU,CAAC;AAEzB,IAAI,iBAAsB;AAE1B,IAAI,sBAAsB;IACxB,iBAAiB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAc,iBAAkB;QAC5D,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;QACA,UAAU;YACR,QAAQ;gBACN,iBAAiB;YACnB;QACF;IACF;AACF,OAAO;IACL,QAAQ,IAAI,CAAC;IACb,yBAAyB;IACzB,iBAAiB;QACf,MAAM;YACJ,QAAQ,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACxF,oBAAoB,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACpG,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;YAC7C,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;gBAAK;YACnE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;QACjF;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBAAE,IAAI,IAAM,CAAC;gCAAE,OAAO,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM,EAAE;wCAAE,OAAO;oCAAK;4BAAG,CAAC;oBAAE,CAAC;gBAC1F,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;gBAC5E,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YAC9E,CAAC;IACH;AACF;AAEO,MAAM,WAAW;AA4JjB,MAAM,OAAO;IAClB,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAAE;YAAO;QAAS;IACtD;IAEA,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;IAClE;IAEA,SAAS;QACP,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;IACpC;IAEA,gBAAgB;QACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,mBAAmB,CAAC;QAClB,OAAO,SAAS,IAAI,CAAC,iBAAiB,CAAC;IACzC;AACF", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User } from '@supabase/supabase-js';\nimport { auth } from '@/lib/supabase';\n\ninterface AuthState {\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  \n  // Actions\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  signOut: () => Promise<void>;\n  setUser: (user: User | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      loading: false,\n      error: null,\n      \n      signIn: async (email: string, password: string) => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { data, error } = await auth.signIn(email, password);\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          if (data.user) {\n            set({ user: data.user, loading: false });\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign in failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      signUp: async (email: string, password: string) => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { data, error } = await auth.signUp(email, password);\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          if (data.user) {\n            set({ user: data.user, loading: false });\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign up failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      signOut: async () => {\n        try {\n          set({ loading: true, error: null });\n          \n          const { error } = await auth.signOut();\n          \n          if (error) {\n            throw new Error(error.message);\n          }\n          \n          set({ user: null, loading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Sign out failed';\n          set({ error: errorMessage, loading: false });\n          throw error;\n        }\n      },\n      \n      setUser: (user: User | null) => {\n        set({ user });\n      },\n      \n      setLoading: (loading: boolean) => {\n        set({ loading });\n      },\n      \n      setError: (error: string | null) => {\n        set({ error });\n      },\n      \n      clearError: () => {\n        set({ error: null });\n      }\n    }),\n    {\n      name: 'timemanager-auth',\n      partialize: (state) => ({\n        user: state.user\n      }),\n      // 使用 localStorage 持久化\n      storage: {\n        getItem: (name) => {\n          if (typeof window !== 'undefined') {\n            const value = localStorage.getItem(name);\n            return value ? JSON.parse(value) : null;\n          }\n          return null;\n        },\n        setItem: (name, value) => {\n          if (typeof window !== 'undefined') {\n            localStorage.setItem(name, JSON.stringify(value));\n          }\n        },\n        removeItem: (name) => {\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem(name);\n          }\n        },\n      },\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAiBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,OAAO;QAEP,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS;YACP,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,OAAI,CAAC,OAAO;gBAEpC,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI;oBAAE,MAAM;oBAAM,SAAS;gBAAM;YACnC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;IACD,sBAAsB;IACtB,SAAS;QACP,SAAS,CAAC;YACR,uCAAmC;;YAGnC;YACA,OAAO;QACT;QACA,SAAS,CAAC,MAAM;YACd,uCAAmC;;YAEnC;QACF;QACA,YAAY,CAAC;YACX,uCAAmC;;YAEnC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augmentProjs/TimeManager/TimeManager/timemanager/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { supabase } from '@/lib/supabase';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, setUser, loading, setLoading } = useAuthStore();\n  const [isMounted, setIsMounted] = useState(false);\n\n  // 防止水合错误\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!isMounted) return;\n\n    const initializeAndRedirect = async () => {\n      console.log('🏠 Home page - initializing...');\n\n      try {\n        setLoading(true);\n\n        // 获取当前用户状态\n        const { data: { user: currentUser }, error } = await supabase.auth.getUser();\n\n        console.log('👤 Current user:', currentUser?.id || 'none');\n\n        if (error) {\n          console.error('❌ Auth error:', error);\n          setUser(null);\n          router.push('/auth/signin');\n          return;\n        }\n\n        setUser(currentUser);\n\n        if (currentUser) {\n          console.log('👤 User logged in, checking onboarding status...');\n          try {\n            const { data: profile, error: profileError } = await supabase\n              .from('user_profiles')\n              .select('onboarding_completed')\n              .eq('id', currentUser.id)\n              .single();\n\n            console.log('📋 Profile data:', profile);\n            console.log('❌ Profile error:', profileError);\n\n            if (profileError) {\n              console.log('🚀 Profile error, redirecting to onboarding...');\n              router.push('/onboarding');\n            } else if (profile?.onboarding_completed) {\n              console.log('✅ Onboarding completed, redirecting to dashboard...');\n              router.push('/dashboard');\n            } else {\n              console.log('📝 Onboarding not completed, redirecting to onboarding...');\n              router.push('/onboarding');\n            }\n          } catch (error) {\n            console.error('❌ Error checking profile:', error);\n            router.push('/onboarding');\n          }\n        } else {\n          console.log('🔐 No user, redirecting to signin...');\n          router.push('/auth/signin');\n        }\n      } catch (error) {\n        console.error('❌ Initialization error:', error);\n        router.push('/auth/signin');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initializeAndRedirect();\n  }, [isMounted, router, setUser, setLoading]);\n\n  // 防止水合错误：在服务端和客户端挂载前显示相同内容\n  if (!isMounted || loading) {\n\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">TimeManager</h1>\n          <p className=\"text-gray-600\">智能时间规划助手</p>\n          <p className=\"text-sm text-gray-500 mt-2\">正在检查登录状态...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // 这里不应该到达，因为上面的 useEffect 会处理重定向\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,wBAAwB;YAC5B,QAAQ,GAAG,CAAC;YAEZ,IAAI;gBACF,WAAW;gBAEX,WAAW;gBACX,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBAE1E,QAAQ,GAAG,CAAC,oBAAoB,aAAa,MAAM;gBAEnD,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,QAAQ;oBACR,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,QAAQ;gBAER,IAAI,aAAa;oBACf,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,iBACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,YAAY,EAAE,EACvB,MAAM;wBAET,QAAQ,GAAG,CAAC,oBAAoB;wBAChC,QAAQ,GAAG,CAAC,oBAAoB;wBAEhC,IAAI,cAAc;4BAChB,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,SAAS,sBAAsB;4BACxC,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAW;QAAQ;QAAS;KAAW;IAE3C,2BAA2B;IAC3B,IAAI,CAAC,aAAa,SAAS;QAEzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,iCAAiC;IACjC,OAAO;AACT", "debugId": null}}]}